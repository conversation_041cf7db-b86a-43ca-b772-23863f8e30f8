// Persistent marker service for reliable map icons
import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/station/station_marker_response.dart';
import '../core/api/api_service.dart';
import '../utils/app_themes.dart';

/// Enhanced service for managing map markers with robust caching
class PersistentMarkerService {
  // Singleton pattern
  static final PersistentMarkerService _instance =
      PersistentMarkerService._internal();
  factory PersistentMarkerService() => _instance;
  PersistentMarkerService._internal();

  // API service for authentication
  final ApiService _apiService = ApiService();

  // In-memory cache of marker images with size limit (50MB)
  final Map<String, Uint8List> _imageCache = {};
  static const int _maxImageCacheSize = 50 * 1024 * 1024; // 50MB limit
  int _currentImageCacheSize = 0;

  // In-memory cache of marker descriptors with size as key part (100 items max)
  final Map<String, BitmapDescriptor> _descriptorCache = {};
  static const int _maxDescriptorCacheSize = 100;

  // Cache of image verification status
  final Map<String, bool> _imageVerificationCache = {};

  // LRU tracking for cache eviction
  final Map<String, DateTime> _imageAccessTimes = {};
  final Map<String, DateTime> _descriptorAccessTimes = {};

  // Cache of marker data
  List<StationMarkerData>? _cachedMarkers;
  DateTime? _lastFetchTime;

  // Timer for periodic cache refresh
  Timer? _cacheRefreshTimer;

  // Timer for image verification
  Timer? _imageVerificationTimer;

  // Cache expiration time (increased to 30 minutes to prevent icon disappearance)
  static const Duration _cacheExpiration = Duration(minutes: 30);

  // Marker preload completed flag
  bool _preloadCompleted = false;

  /// Initialize the service and start cache maintenance
  Future<void> initialize() async {
    debugPrint('Initializing PersistentMarkerService');

    try {
      // Load images from persistent storage into memory cache
      await _loadImagesFromPersistentCache();

      // Verify cache integrity immediately
      await _verifyImageIntegrity();

      // Set up more frequent cache refresh (every 1 minute)
      _cacheRefreshTimer?.cancel();
      _cacheRefreshTimer =
          Timer.periodic(const Duration(minutes: 1), (_) async {
        await _refreshImportantMarkers();
      });

      // Set up periodic image verification (more frequent - every 5 minutes)
      _imageVerificationTimer?.cancel();
      _imageVerificationTimer =
          Timer.periodic(const Duration(minutes: 5), (_) async {
        await _verifyImageIntegrity();
      });

      // Preload common marker images
      await preloadCommonMarkers();

      debugPrint(
          'PersistentMarkerService initialization completed successfully');
    } catch (e) {
      debugPrint('Error initializing PersistentMarkerService: $e');
      // Try to recover by preloading common markers anyway
      try {
        await preloadCommonMarkers();
      } catch (e) {
        debugPrint('Error during recovery preload: $e');
      }
    }
  }

  /// Load images from persistent cache into memory
  Future<void> _loadImagesFromPersistentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Find all marker image keys
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('marker_image_'))
          .toList();

      if (keys.isNotEmpty) {
        debugPrint(
            'Loading ${keys.length} marker images from persistent cache');

        int loadedCount = 0;
        for (final key in keys) {
          final base64Image = prefs.getString(key);
          if (base64Image != null) {
            try {
              final imageData = base64Decode(base64Image);
              final url = key.replaceFirst('marker_image_', '');
              _imageCache[url] = imageData;
              loadedCount++;
            } catch (e) {
              // Skip corrupt entries
              await prefs.remove(key);
            }
          }
        }

        debugPrint(
            'Successfully loaded $loadedCount marker images from persistent cache');
      } else {
        debugPrint('No marker images found in persistent cache');
      }
    } catch (e) {
      debugPrint('Error loading images from persistent cache: $e');
    }
  }

  /// Refresh important markers - called periodically
  Future<void> _refreshImportantMarkers() async {
    try {
      debugPrint('Refreshing important markers in cache');

      // Centralized configuration for all marker icon URLs
      final Map<String, List<String>> markerIconConfig = {
        'available': [
          'https://api2.eeil.online/mapicons/ecoplug_available.png',
          'https://api2.eeil.online/icons/available.png',
          'https://api2.eeil.online/mapicons/ecoplug_default.png',
        ],
        'unavailable': [
          'https://api2.eeil.online/mapicons/ecoplug_unavailable.png',
          'https://api2.eeil.online/icons/unavailable.png',
          'https://api2.eeil.online/mapicons/unavailable.png',
        ],
        'charging': [
          'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png',
          'https://api2.eeil.online/mapicons/ecoplug_charging.png',
          'https://api2.eeil.online/icons/charging.png',
          'https://api2.eeil.online/mapicons/charging.png',
        ],
        'charging_focus': [
          'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png',
          'https://api2.eeil.online/mapicons/ecoplug_charging_focus.png',
          'https://api2.eeil.online/icons/charging_focus.png',
        ],
        'focus': [
          'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          'https://api2.eeil.online/icons/focus.png',
          'https://api2.eeil.online/mapicons/focus.png',
        ],
        'default': [
          'https://api2.eeil.online/mapicons/ecoplug_default.png',
          'https://api2.eeil.online/icons/default.png',
        ],
        'nearest': [
          'https://api2.eeil.online/mapicons/ecoplug_nearest.png',
          'https://api2.eeil.online/icons/nearest.png',
        ],
      };

      // Flatten the configuration into a list of all URLs to check
      final List<String> importantMarkerUrls = [];
      markerIconConfig.forEach((key, urls) {
        importantMarkerUrls.addAll(urls);
      });

      // Log the URLs we're checking
      debugPrint('Checking ${importantMarkerUrls.length} marker icon URLs');

      // Use the flattened list directly
      final allCriticalUrls = importantMarkerUrls;

      // Check which markers need refreshing (missing from cache, failed verification, or approaching expiration)
      final urlsToRefresh = allCriticalUrls
          .where((url) =>
              !_imageCache.containsKey(url) ||
              _imageVerificationCache[url] == false)
          .toList();

      if (urlsToRefresh.isNotEmpty) {
        debugPrint(
            'Refreshing ${urlsToRefresh.length} important marker images');

        // Fetch each missing marker
        for (final url in urlsToRefresh) {
          await getMarkerImage(url, forceRefresh: true);
          // Add a small delay between requests
          await Future.delayed(const Duration(milliseconds: 75));
        }

        // Also update bitmap descriptors for these URLs
        for (final url in urlsToRefresh) {
          await getBitmapDescriptorFromUrl(url, forceRefresh: true);
          await Future.delayed(const Duration(milliseconds: 30));
        }
      }
    } catch (e) {
      debugPrint('Error refreshing important markers: $e');
      // Try to recover
      await preloadCommonMarkers();
    }
  }

  /// Verify the integrity of cached images
  Future<void> _verifyImageIntegrity() async {
    try {
      debugPrint('Verifying integrity of cached marker images');

      // Get list of URLs to verify
      final urlsToVerify = _imageCache.keys.toList();

      if (urlsToVerify.isEmpty) {
        return;
      }

      int verifiedCount = 0;
      int failedCount = 0;

      // Verify a subset of images each time (max 20)
      final verificationBatch = urlsToVerify.take(20).toList();

      for (final url in verificationBatch) {
        final imageData = _imageCache[url];

        if (imageData != null) {
          try {
            // Simple verification - check if image data is valid
            if (imageData.lengthInBytes < 100) {
              // Too small to be a valid image, mark as failed
              _imageVerificationCache[url] = false;
              failedCount++;
            } else {
              // Image data seems valid
              _imageVerificationCache[url] = true;
              verifiedCount++;
            }
          } catch (e) {
            debugPrint('Error verifying image for URL $url: $e');
            _imageVerificationCache[url] = false;
            failedCount++;
          }
        }
      }

      debugPrint(
          'Image verification complete: $verifiedCount passed, $failedCount failed');

      // Refresh failed images
      if (failedCount > 0) {
        final failedUrls = _imageVerificationCache.entries
            .where((entry) => entry.value == false)
            .map((entry) => entry.key)
            .toList();

        debugPrint('Refreshing ${failedUrls.length} failed images');

        for (final url in failedUrls) {
          await getMarkerImage(url, forceRefresh: true);
          // Add a small delay between requests
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
    } catch (e) {
      debugPrint('Error verifying image integrity: $e');
    }
  }

  /// Get cached markers or fetch new ones if cache is expired
  Future<List<StationMarkerData>> getMarkers(
      Future<List<StationMarkerData>> Function() fetchFunction) async {
    // Check if we have cached markers and they're not expired
    if (_cachedMarkers != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheExpiration) {
      debugPrint('Using cached markers (${_cachedMarkers!.length})');
      return _cachedMarkers!;
    }

    // Fetch new markers
    debugPrint('Fetching fresh markers from API');
    try {
      final markers = await fetchFunction();
      _cachedMarkers = markers;
      _lastFetchTime = DateTime.now();

      // Pre-fetch marker images in the background
      _prefetchMarkerImages(markers);

      return markers;
    } catch (e) {
      debugPrint('Error fetching markers: $e');
      // Return cached markers if available, even if expired
      if (_cachedMarkers != null) {
        debugPrint('Returning expired cached markers due to fetch error');
        return _cachedMarkers!;
      }
      rethrow;
    }
  }

  /// Pre-fetch marker images to ensure they're available
  Future<void> _prefetchMarkerImages(List<StationMarkerData> markers) async {
    // Collect unique image URLs
    final Set<String> uniqueUrls = {};
    for (final marker in markers) {
      if (marker.mapPinUrl != null && marker.mapPinUrl!.isNotEmpty) {
        uniqueUrls.add(_normalizeUrl(marker.mapPinUrl!));
      }
      if (marker.focusedMapPinUrl != null &&
          marker.focusedMapPinUrl!.isNotEmpty) {
        uniqueUrls.add(_normalizeUrl(marker.focusedMapPinUrl!));
      }
    }

    // Only log if there are URLs to preload
    if (uniqueUrls.isNotEmpty) {
      debugPrint('Preloading ${uniqueUrls.length} unique marker images');
    }

    // Load images in small batches to avoid overwhelming the network
    const int batchSize = 5;
    for (int i = 0; i < uniqueUrls.length; i += batchSize) {
      final batch = uniqueUrls.skip(i).take(batchSize).toList();
      int successCount = 0;

      // Process sequentially to be gentle on mobile resources
      for (final url in batch) {
        try {
          final image = await getMarkerImage(url);
          if (image != null) {
            successCount++;
          }
        } catch (e) {
          debugPrint('Error preloading marker image: $e');
        }
      }

      if (successCount > 0) {
        debugPrint(
            'Preloaded $successCount marker images in batch ${(i ~/ batchSize) + 1}');
      }

      // Add a delay between batches
      await Future.delayed(const Duration(milliseconds: 200));
    }
  }

  /// Get a marker image with retries and caching
  Future<Uint8List?> getMarkerImage(String url,
      {bool forceRefresh = false}) async {
    // Normalize URL
    url = _normalizeUrl(url);

    // Check if URL is valid
    if (url.isEmpty || !url.startsWith('http')) {
      debugPrint('Invalid marker image URL: $url');
      return _createFallbackMarkerImage(url);
    }

    debugPrint(
        'Getting marker image for URL: $url (forceRefresh: $forceRefresh)');

    // Check memory cache first (unless forcing refresh)
    if (!forceRefresh && _imageCache.containsKey(url)) {
      debugPrint('Found marker image in memory cache for: $url');
      _imageAccessTimes[url] = DateTime.now(); // Update LRU
      return _imageCache[url];
    }

    // Check persistent cache next (unless forcing refresh)
    if (!forceRefresh) {
      try {
        final persistentImage = await _getImageFromPersistentCache(url);
        if (persistentImage != null) {
          debugPrint('Found marker image in persistent cache for: $url');
          _imageCache[url] = persistentImage;
          // Mark as verified
          _imageVerificationCache[url] = true;
          return persistentImage;
        } else {
          debugPrint('No marker image found in persistent cache for: $url');
        }
      } catch (e) {
        debugPrint('Error reading from persistent cache: $e');
        // Continue to network fetch if cache read fails
      }
    }

    // If not in cache or forcing refresh, fetch from network with retries
    try {
      // Add authorization header for API2 URLs
      Map<String, String> headers = {};
      if (url.contains('api2.eeil.online')) {
        final token = await _apiService.getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
          headers['Content-Type'] = 'text/plain';
        }
      }

      // Implement a robust retry mechanism
      int retryCount = 0;
      const maxRetries = 3;
      const initialRetryDelay = 500; // milliseconds

      // Get alternative URLs based on the marker type
      List<String> urlsToTry = _getAlternativeUrls(url);

      // Try each URL with retries
      for (final currentUrl in urlsToTry) {
        debugPrint('Trying URL: $currentUrl');
        retryCount = 0;

        while (retryCount <= maxRetries) {
          try {
            // Use a shorter timeout for better UX
            final timeout = const Duration(seconds: 3);

            final response = await http
                .get(
                  Uri.parse(currentUrl),
                  headers: headers,
                )
                .timeout(timeout)
                .catchError((error) {
              debugPrint('HTTP error for $currentUrl: $error');
              throw error; // Re-throw to be caught by the outer try-catch
            });

            if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
              final imageData = response.bodyBytes;

              // Basic image data validation
              if (imageData.lengthInBytes < 100) {
                debugPrint(
                    'Warning: Image data for $currentUrl is suspiciously small (${imageData.lengthInBytes} bytes)');
                retryCount++;
                continue;
              }

              // Store in memory cache for both the original URL and current URL
              _imageCache[url] = imageData;
              _currentImageCacheSize += imageData.length;
              _imageAccessTimes[url] = DateTime.now();
              if (currentUrl != url) {
                _imageCache[currentUrl] = imageData;
                _currentImageCacheSize += imageData.length;
                _imageAccessTimes[currentUrl] = DateTime.now();
              }

              // Manage cache size
              _manageImageCacheSize();

              // Save to persistent cache for both URLs
              await _saveImageToPersistentCache(url, imageData);
              if (currentUrl != url) {
                await _saveImageToPersistentCache(currentUrl, imageData);
              }

              // Mark as verified
              _imageVerificationCache[url] = true;
              if (currentUrl != url) {
                _imageVerificationCache[currentUrl] = true;
              }

              debugPrint('Successfully fetched marker image from: $currentUrl');
              return imageData;
            } else if (response.statusCode == 404) {
              // If we get a 404, log it and continue to the next retry or URL
              debugPrint('404 Not Found for URL: $currentUrl');

              // Mark this URL as failed in verification cache
              _imageVerificationCache[currentUrl] = false;

              // Exit the retry loop for this URL
              break;
            } else {
              debugPrint(
                  'HTTP error fetching marker image from $currentUrl: ${response.statusCode}');
            }
          } catch (e) {
            debugPrint(
                'Error fetching marker image from $currentUrl (attempt ${retryCount + 1}): $e');
          }

          // Increment retry count and delay with exponential backoff
          retryCount++;
          if (retryCount <= maxRetries) {
            final delay = initialRetryDelay * (1 << retryCount);
            debugPrint('Retrying in ${delay}ms...');
            await Future.delayed(Duration(milliseconds: delay));
          }
        }

        // All retries for this URL failed, try the next URL
        debugPrint('All retries failed for URL: $currentUrl');
      }

      // All URLs and retries failed, mark as failed in verification cache
      _imageVerificationCache[url] = false;
      debugPrint('All URLs failed, using fallback image for: $url');

      // Create fallback image
      final fallbackImage = await _createFallbackMarkerImage(url);

      // Cache the fallback image to prevent repeated failures
      _imageCache[url] = fallbackImage;
      await _saveImageToPersistentCache(url, fallbackImage);

      return fallbackImage;
    } catch (e) {
      debugPrint('Fatal error fetching marker image: $e');
      _imageVerificationCache[url] = false;

      // Create fallback image
      final fallbackImage = await _createFallbackMarkerImage(url);

      // Cache the fallback image
      _imageCache[url] = fallbackImage;
      await _saveImageToPersistentCache(url, fallbackImage);

      return fallbackImage;
    }
  }

  /// Get BitmapDescriptor for marker
  Future<BitmapDescriptor?> getBitmapDescriptorFromUrl(
    String url, {
    double width = 25.0,
    double height = 37.0,
    bool forceRefresh = false,
  }) async {
    // Normalize URL
    final String normalizedUrl = _normalizeUrl(url);
    url = normalizedUrl;

    // Create a cache key that includes the size
    final cacheKey = '${url}_${width.toInt()}_${height.toInt()}';

    // Check cache first (unless forcing refresh)
    if (!forceRefresh && _descriptorCache.containsKey(cacheKey)) {
      _descriptorAccessTimes[cacheKey] = DateTime.now(); // Update LRU
      return _descriptorCache[cacheKey];
    }

    // Get the image data (may use forced refresh if requested)
    final Uint8List? imageData =
        await getMarkerImage(url, forceRefresh: forceRefresh);

    if (imageData == null || imageData.isEmpty) {
      // Use fallback descriptor - must await since it's now async
      return await getFallbackDescriptor(url);
    }

    try {
      // Create BitmapDescriptor with specified size
      final descriptor = BitmapDescriptor.bytes(
        imageData,
        width: width,
        height: height,
      );
      // Cache the descriptor with LRU tracking
      _descriptorCache[cacheKey] = descriptor;
      _descriptorAccessTimes[cacheKey] = DateTime.now();

      // Manage cache size
      _manageDescriptorCacheSize();

      return descriptor;
    } catch (e) {
      debugPrint('Error creating BitmapDescriptor: $e');
      // Must use await with async getFallbackDescriptor
      return await getFallbackDescriptor(url);
    }
  }

  /// Get a BitmapDescriptor for a specific station status
  Future<BitmapDescriptor?> getMarkerDescriptorForStatus(
    String status, {
    bool focused = false,
    double width = 40.0,
    double height = 40.0,
    bool forceRefresh = false,
  }) async {
    // Use direct API URLs without fallbacks
    String url;

    // Log the status for debugging
    debugPrint(
        'Getting marker descriptor for status: $status, focused: $focused');

    if (focused) {
      url = 'https://api2.eeil.online/mapicons/ecoplug_focus.png';
    } else {
      if (status.toLowerCase() == 'unavailable') {
        url = 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png';
      } else if (status.toLowerCase().contains('in use') ||
          status.toLowerCase().contains('charging')) {
        url =
            'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png';
      } else {
        url = 'https://api2.eeil.online/mapicons/ecoplug_available.png';
      }
    }

    // Log the URL being used
    debugPrint('Using marker URL: $url');

    return getBitmapDescriptorFromUrl(url,
        width: width, height: height, forceRefresh: forceRefresh);
  }

  /// Save image to persistent cache
  Future<void> _saveImageToPersistentCache(
      String url, Uint8List imageData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'marker_image_${_normalizeUrl(url)}';
      final base64Image = base64Encode(imageData);
      await prefs.setString(key, base64Image);
    } catch (e) {
      debugPrint('Error saving image to persistent cache: $e');
    }
  }

  /// Get image from persistent cache
  Future<Uint8List?> _getImageFromPersistentCache(String url) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'marker_image_${_normalizeUrl(url)}';
      final base64Image = prefs.getString(key);
      if (base64Image != null) {
        return base64Decode(base64Image);
      }
    } catch (e) {
      debugPrint('Error getting image from persistent cache: $e');
    }
    return null;
  }

  /// Create a fallback marker image if network fetch fails
  Future<Uint8List> _createFallbackMarkerImage(String url) async {
    try {
      // Determine marker type and color based on URL
      String markerType = 'default';
      Color color = AppThemes.primaryColor;

      if (url.toLowerCase().contains('unavailable') ||
          url.toLowerCase().contains('error')) {
        color = Colors.red;
        markerType = 'unavailable';
      } else if (url.toLowerCase().contains('charging')) {
        color = Colors.blue;
        markerType = 'charging';
      } else if (url.toLowerCase().contains('focus')) {
        color = Colors.orange;
        markerType = 'focus';
      } else if (url.toLowerCase().contains('available')) {
        color = Colors.green;
        markerType = 'available';
      }

      debugPrint(
          'Creating fallback marker for type: $markerType with color: $color');

      // Try to create a custom marker with the appropriate shape
      try {
        final ui.PictureRecorder recorder = ui.PictureRecorder();
        final Canvas canvas = Canvas(recorder);
        final double width = 48.0;
        final double height = 48.0;

        // Create a pin shape based on marker type
        final Path pinPath = Path();

        if (markerType == 'focus') {
          // Create a star shape for focused markers
          const double centerX = 24.0;
          const double centerY = 24.0;
          const double outerRadius = 20.0;
          const double innerRadius = 10.0;
          const int numPoints = 5;

          for (int i = 0; i < numPoints * 2; i++) {
            final double radius = i.isEven ? outerRadius : innerRadius;
            final double angle = i * math.pi / numPoints;
            final double x = centerX + radius * math.cos(angle);
            final double y = centerY + radius * math.sin(angle);

            if (i == 0) {
              pinPath.moveTo(x, y);
            } else {
              pinPath.lineTo(x, y);
            }
          }
          pinPath.close();
        } else {
          // Create a pin shape for other markers
          pinPath.moveTo(width * 0.5, height * 0.1); // Top center
          pinPath.addOval(Rect.fromCircle(
            center: Offset(width * 0.5, height * 0.3),
            radius: width * 0.2,
          ));

          // Add the pin stem
          pinPath.moveTo(width * 0.5, height * 0.5);
          pinPath.lineTo(width * 0.4, height * 0.8);
          pinPath.lineTo(width * 0.6, height * 0.8);
          pinPath.close();
        }

        // Fill the pin
        final Paint fillPaint = Paint()
          ..color = color
          ..style = PaintingStyle.fill;
        canvas.drawPath(pinPath, fillPaint);

        // Add border
        final Paint borderPaint = Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;
        canvas.drawPath(pinPath, borderPaint);

        // Add a symbol in the center based on marker type
        if (markerType == 'charging') {
          // Add a lightning bolt symbol
          final Path boltPath = Path();
          boltPath.moveTo(width * 0.5, height * 0.2);
          boltPath.lineTo(width * 0.4, height * 0.35);
          boltPath.lineTo(width * 0.5, height * 0.35);
          boltPath.lineTo(width * 0.5, height * 0.45);
          boltPath.lineTo(width * 0.6, height * 0.3);
          boltPath.lineTo(width * 0.5, height * 0.3);
          boltPath.close();

          final Paint boltPaint = Paint()
            ..color = Colors.white
            ..style = PaintingStyle.fill;
          canvas.drawPath(boltPath, boltPaint);
        } else if (markerType == 'unavailable') {
          // Add an X symbol
          final Paint xPaint = Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2;

          canvas.drawLine(
            Offset(width * 0.4, height * 0.25),
            Offset(width * 0.6, height * 0.35),
            xPaint,
          );

          canvas.drawLine(
            Offset(width * 0.6, height * 0.25),
            Offset(width * 0.4, height * 0.35),
            xPaint,
          );
        }

        // Complete the image
        final ui.Image img = await recorder
            .endRecording()
            .toImage(width.toInt(), height.toInt());
        final ByteData? data =
            await img.toByteData(format: ui.ImageByteFormat.png);

        if (data != null) {
          final bytes = data.buffer.asUint8List();
          // Cache this fallback image
          _imageCache[url] = bytes;
          return bytes;
        }
      } catch (e) {
        debugPrint('Error creating custom fallback marker: $e');
      }

      // If custom marker creation fails, fall back to a simple colored circle
      debugPrint('Falling back to simple colored circle marker');
      final int size = 48;
      final Uint8List bytes = Uint8List(size * size * 4);

      // Fill with transparent pixels
      for (int i = 0; i < bytes.length; i += 4) {
        bytes[i] = 0; // R
        bytes[i + 1] = 0; // G
        bytes[i + 2] = 0; // B
        bytes[i + 3] = 0; // A (transparent)
      }

      // Determine color based on URL
      int r = color.r.toInt();
      int g = color.g.toInt();
      int b = color.b.toInt();

      // Draw a circle in the center
      final int centerX = size ~/ 2;
      final int centerY = size ~/ 2;
      final int radius = size ~/ 3;

      for (int y = 0; y < size; y++) {
        for (int x = 0; x < size; x++) {
          final int dx = x - centerX;
          final int dy = y - centerY;
          final int distSquared = dx * dx + dy * dy;

          if (distSquared <= radius * radius) {
            final int index = (y * size + x) * 4;
            bytes[index] = r;
            bytes[index + 1] = g;
            bytes[index + 2] = b;
            bytes[index + 3] = 255; // Opaque
          }
        }
      }

      // Cache this fallback image
      _imageCache[url] = bytes;
      return bytes;
    } catch (e) {
      debugPrint('Error creating fallback marker: $e');
      // Return a minimal 1x1 colored pixel as last resort
      return Uint8List.fromList([0, 255, 0, 255]); // Green pixel
    }
  }

  /// Create custom in-memory markers - NO default Google markers ever used
  /// Even in case of failure, we use our own generated markers
  Future<BitmapDescriptor> getFallbackDescriptor(String url) async {
    try {
      // Always create a custom programmatic marker rather than using any default or potential fallback
      Color markerColor;

      if (url.toLowerCase().contains('unavailable')) {
        markerColor = Colors.red;
      } else if (url.toLowerCase().contains('charging')) {
        markerColor = Colors.orange;
      } else if (url.toLowerCase().contains('focus')) {
        markerColor = Colors.blue;
      } else {
        markerColor = Colors.green;
      }

      // Create a completely custom marker programmatically
      // This avoids any reliance on assets or default markers
      return await _createCustomMarker(markerColor);
    } catch (e) {
      debugPrint('Failed to create primary custom marker: $e');
      // Absolute fallback still creates a custom marker
      // Never use any Google default marker
      return await _createSimpleMarker(url.toLowerCase().contains('unavailable')
          ? Colors.red
          : Colors.green);
    }
  }

  /// Create a simple marker - used as a fallback if the custom marker fails
  Future<BitmapDescriptor> _createSimpleMarker(Color color) async {
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Create a simple circle
    canvas.drawCircle(const Offset(24, 24), 12, paint);

    // Add white border
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawCircle(const Offset(24, 24), 12, borderPaint);

    final ui.Image img = await recorder.endRecording().toImage(48, 48);
    final ByteData? data = await img.toByteData(format: ui.ImageByteFormat.png);

    if (data == null) {
      // If even this fails, create a 2x2 colored pixel
      // NEVER use Google default markers
      return BitmapDescriptor.bytes(Uint8List.fromList([
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
      ]));
    }

    return BitmapDescriptor.bytes(data.buffer.asUint8List());
  }

  /// Create a custom marker with a distinctive pin shape
  /// This is the primary marker generator, completely avoiding default markers
  Future<BitmapDescriptor> _createCustomMarker(Color color) async {
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final double width = 48.0;
    final double height = 48.0;

    // Create a pin shape
    final Path pinPath = Path();
    pinPath.moveTo(width * 0.5, height * 0.1); // Top center
    pinPath.addOval(Rect.fromCircle(
      center: Offset(width * 0.5, height * 0.3),
      radius: width * 0.2,
    ));

    // Add the pin stem
    pinPath.moveTo(width * 0.5, height * 0.5);
    pinPath.lineTo(width * 0.4, height * 0.8);
    pinPath.lineTo(width * 0.6, height * 0.8);
    pinPath.close();

    // Fill the pin
    final Paint fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    canvas.drawPath(pinPath, fillPaint);

    // Add border
    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawPath(pinPath, borderPaint);

    // Complete the image
    final ui.Image img =
        await recorder.endRecording().toImage(width.toInt(), height.toInt());
    final ByteData? data = await img.toByteData(format: ui.ImageByteFormat.png);

    if (data == null) {
      // Fall back to simple marker if this fails, but NEVER use Google default
      return await _createSimpleMarker(color);
    }

    return BitmapDescriptor.bytes(data.buffer.asUint8List());
  }

  /// Get alternative URLs for a marker based on its type
  List<String> _getAlternativeUrls(String originalUrl) {
    // Start with the original URL
    List<String> urls = [originalUrl];

    // Define marker types and their alternative URL patterns
    final Map<String, List<String>> markerIconConfig = {
      'available': [
        'https://api2.eeil.online/mapicons/ecoplug_available.png',
        'https://api2.eeil.online/icons/available.png',
        'https://api2.eeil.online/mapicons/ecoplug_default.png',
      ],
      'unavailable': [
        'https://api2.eeil.online/mapicons/ecoplug_unavailable.png',
        'https://api2.eeil.online/icons/unavailable.png',
        'https://api2.eeil.online/mapicons/unavailable.png',
      ],
      'charging': [
        'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png',
        'https://api2.eeil.online/mapicons/ecoplug_charging.png',
        'https://api2.eeil.online/icons/charging.png',
        'https://api2.eeil.online/mapicons/charging.png',
      ],
      'charging_focus': [
        'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png',
        'https://api2.eeil.online/mapicons/ecoplug_charging_focus.png',
        'https://api2.eeil.online/icons/charging_focus.png',
      ],
      'focus': [
        'https://api2.eeil.online/mapicons/ecoplug_focus.png',
        'https://api2.eeil.online/icons/focus.png',
        'https://api2.eeil.online/mapicons/focus.png',
      ],
      'default': [
        'https://api2.eeil.online/mapicons/ecoplug_default.png',
        'https://api2.eeil.online/icons/default.png',
      ],
    };

    // Determine which marker type this URL belongs to
    String? markerType;

    if (originalUrl.contains('available')) {
      markerType = 'available';
    } else if (originalUrl.contains('unavailable')) {
      markerType = 'unavailable';
    } else if (originalUrl.contains('charging') &&
        originalUrl.contains('focus')) {
      markerType = 'charging_focus';
    } else if (originalUrl.contains('charging')) {
      markerType = 'charging';
    } else if (originalUrl.contains('focus')) {
      markerType = 'focus';
    } else {
      markerType = 'default';
    }

    // Add all alternative URLs for this marker type
    if (markerIconConfig.containsKey(markerType)) {
      // Add all URLs except the original one (which is already in the list)
      for (final url in markerIconConfig[markerType]!) {
        if (url != originalUrl && !urls.contains(url)) {
          urls.add(url);
        }
      }
    }

    // Log all URLs we're going to try
    debugPrint('Alternative URLs for $originalUrl: ${urls.join(', ')}');

    return urls;
  }

  /// Normalize URL for caching
  String _normalizeUrl(String url) {
    // Ensure URL starts with http/https
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    // Remove any trailing slashes or whitespace
    url = url.trim();
    while (url.endsWith('/')) {
      url = url.substring(0, url.length - 1);
    }

    // Ensure URL doesn't have double slashes in the path (but keep http:// or https://)
    if (url.startsWith('http://')) {
      url = 'http://${url.substring(7).replaceAll('//', '/')}';
    } else if (url.startsWith('https://')) {
      url = 'https://${url.substring(8).replaceAll('//', '/')}';
    }

    return url;
  }

  /// Preload common marker images to avoid delays
  Future<void> preloadCommonMarkers() async {
    // If already preloaded, don't do it again
    if (_preloadCompleted) {
      debugPrint('Common markers already preloaded, skipping');
      return;
    }

    // Core marker URLs that should always be available
    final commonUrls = [
      // Primary marker icons
      'https://api2.eeil.online/mapicons/ecoplug_available.png',
      'https://api2.eeil.online/mapicons/ecoplug_unavailable.png',
      'https://api2.eeil.online/mapicons/ecoplug_charging.png',
      'https://api2.eeil.online/mapicons/ecoplug_default.png',
      'https://api2.eeil.online/mapicons/ecoplug_focus.png',
      'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png',
      'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png',

      // Fallback icons
      'https://api2.eeil.online/icons/available.png',
      'https://api2.eeil.online/icons/unavailable.png',
      'https://api2.eeil.online/icons/charging.png',
      'https://api2.eeil.online/icons/default.png',
      'https://api2.eeil.online/icons/focus.png',
    ];

    debugPrint('Preloading ${commonUrls.length} common marker images');

    int successCount = 0;
    int failureCount = 0;

    // Load in small batches to avoid overwhelming network on app start
    const int batchSize = 3;
    for (int i = 0; i < commonUrls.length; i += batchSize) {
      final batch = commonUrls.skip(i).take(batchSize).toList();

      // Process batch in parallel for efficiency
      final results = await Future.wait(
        batch.map((url) async {
          try {
            final data = await getMarkerImage(url);
            if (data != null) {
              // Also create BitmapDescriptor and cache
              await getBitmapDescriptorFromUrl(url);
              return true;
            }
          } catch (e) {
            debugPrint('Error preloading marker: $e');
          }
          return false;
        }),
        eagerError: false,
      );

      // Count successes and failures
      successCount += results.where((success) => success).length;
      failureCount += results.where((success) => !success).length;

      // Add a small delay between batches
      if (i + batchSize < commonUrls.length) {
        await Future.delayed(const Duration(milliseconds: 200));
      }
    }

    debugPrint(
        'Preload complete: $successCount succeeded, $failureCount failed out of ${commonUrls.length} common marker images');

    // Mark as completed even if some failed - we'll retry those during normal operation
    _preloadCompleted = true;
  }

  /// Clean up resources
  void dispose() {
    _cacheRefreshTimer?.cancel();
    _imageVerificationTimer?.cancel();
  }

  /// Clear the cache
  Future<void> clearCache() async {
    _imageCache.clear();
    _descriptorCache.clear();
    _imageVerificationCache.clear();
    _cachedMarkers = null;
    _lastFetchTime = null;
    _preloadCompleted = false;

    // Clear persistent cache
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('marker_image_'))
          .toList();

      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('Cleared ${keys.length} marker images from persistent cache');
    } catch (e) {
      debugPrint('Error clearing persistent cache: $e');
    }
  }

  /// Check if a specific marker URL is available in the cache
  bool isMarkerCached(String url) {
    url = _normalizeUrl(url);
    return _imageCache.containsKey(url);
  }

  /// Get statistics about the cache state
  Map<String, dynamic> getCacheStatistics() {
    return {
      'imageCount': _imageCache.length,
      'descriptorCount': _descriptorCache.length,
      'verifiedCount':
          _imageVerificationCache.values.where((v) => v == true).length,
      'failedCount':
          _imageVerificationCache.values.where((v) => v == false).length,
      'preloadCompleted': _preloadCompleted,
    };
  }

  /// Create a custom marker programmatically - NEVER use default Google markers
  Future<BitmapDescriptor> createCustomMarker(Color color) async {
    try {
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);
      final double width = 48.0;
      final double height = 48.0;

      // Create a pin shape
      final Path pinPath = Path();
      pinPath.moveTo(width * 0.5, height * 0.1); // Top center
      pinPath.addOval(Rect.fromCircle(
        center: Offset(width * 0.5, height * 0.3),
        radius: width * 0.2,
      ));

      // Add the pin stem
      pinPath.moveTo(width * 0.5, height * 0.5);
      pinPath.lineTo(width * 0.4, height * 0.8);
      pinPath.lineTo(width * 0.6, height * 0.8);
      pinPath.close();

      // Fill the pin
      final Paint fillPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;
      canvas.drawPath(pinPath, fillPaint);

      // Add border
      final Paint borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawPath(pinPath, borderPaint);

      // Complete the image
      final ui.Image img =
          await recorder.endRecording().toImage(width.toInt(), height.toInt());
      final ByteData? data =
          await img.toByteData(format: ui.ImageByteFormat.png);

      if (data == null) {
        // Fall back to simple marker if this fails
        return await createSimpleMarker(color);
      }

      return BitmapDescriptor.bytes(data.buffer.asUint8List());
    } catch (e) {
      debugPrint('Error creating custom marker: $e');
      return await createSimpleMarker(color);
    }
  }

  /// Create a simple circular marker
  Future<BitmapDescriptor> createSimpleMarker(Color color) async {
    try {
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);
      final Paint paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // Create a simple circle
      canvas.drawCircle(const Offset(24, 24), 12, paint);

      // Add white border
      final Paint borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      canvas.drawCircle(const Offset(24, 24), 12, borderPaint);

      final ui.Image img = await recorder.endRecording().toImage(48, 48);
      final ByteData? data =
          await img.toByteData(format: ui.ImageByteFormat.png);

      if (data == null) {
        // If even this fails, create a 2x2 colored pixel
        return BitmapDescriptor.bytes(Uint8List.fromList([
          color.r.toInt(),
          color.g.toInt(),
          color.b.toInt(),
          255,
          color.r.toInt(),
          color.g.toInt(),
          color.b.toInt(),
          255,
          color.r.toInt(),
          color.g.toInt(),
          color.b.toInt(),
          255,
          color.r.toInt(),
          color.g.toInt(),
          color.b.toInt(),
          255,
        ]));
      }

      return BitmapDescriptor.bytes(data.buffer.asUint8List());
    } catch (e) {
      debugPrint('Error creating simple marker: $e');
      // Last resort - create a minimal colored pixel
      return BitmapDescriptor.bytes(Uint8List.fromList([
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
        color.r.toInt(),
        color.g.toInt(),
        color.b.toInt(),
        255,
      ]));
    }
  }

  /// Manage image cache size using LRU eviction
  void _manageImageCacheSize() {
    // Check if we need to evict based on size or count
    if (_currentImageCacheSize > _maxImageCacheSize ||
        _imageCache.length > 200) {
      // Sort by access time (LRU)
      final sortedEntries = _imageAccessTimes.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest entries until we're under limits
      int removedSize = 0;
      int removedCount = 0;

      for (final entry in sortedEntries) {
        if (_currentImageCacheSize <= _maxImageCacheSize * 0.8 &&
            _imageCache.length <= 150) {
          break; // Stop when we're comfortably under limits
        }

        final url = entry.key;
        final imageData = _imageCache[url];
        if (imageData != null) {
          removedSize += imageData.length;
          _currentImageCacheSize -= imageData.length;
          removedCount++;
        }

        _imageCache.remove(url);
        _imageAccessTimes.remove(url);
      }

      if (removedCount > 0) {
        debugPrint(
            '🧹 IMAGE CACHE: Removed $removedCount images (${(removedSize / 1024).toStringAsFixed(1)}KB) via LRU');
      }
    }
  }

  /// Manage descriptor cache size using LRU eviction
  void _manageDescriptorCacheSize() {
    if (_descriptorCache.length > _maxDescriptorCacheSize) {
      // Sort by access time (LRU)
      final sortedEntries = _descriptorAccessTimes.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest entries
      final entriesToRemove =
          _descriptorCache.length - (_maxDescriptorCacheSize * 0.8).toInt();

      for (int i = 0; i < entriesToRemove && i < sortedEntries.length; i++) {
        final key = sortedEntries[i].key;
        _descriptorCache.remove(key);
        _descriptorAccessTimes.remove(key);
      }

      if (entriesToRemove > 0) {
        debugPrint(
            '🧹 DESCRIPTOR CACHE: Removed $entriesToRemove descriptors via LRU');
      }
    }
  }
}
