# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# ===== GENERATED MISSING RULES FROM R8 =====
# These rules suppress warnings for missing classes
-dontwarn com.google.android.gms.auth.api.credentials.Credential
-dontwarn com.google.android.gms.auth.api.credentials.Credentials
-dontwarn com.google.android.gms.auth.api.credentials.CredentialsClient
-dontwarn com.google.android.gms.auth.api.credentials.CredentialsOptions$Builder
-dontwarn com.google.android.gms.auth.api.credentials.CredentialsOptions
-dontwarn com.google.android.gms.auth.api.credentials.HintRequest$Builder
-dontwarn com.google.android.gms.auth.api.credentials.HintRequest
-dontwarn com.payu.cardscanner.PayU
-dontwarn com.payu.cardscanner.callbacks.PayUCardListener
-dontwarn com.payu.olamoney.OlaMoney
-dontwarn com.payu.olamoney.callbacks.OlaMoneyCallback
-dontwarn com.payu.olamoney.utils.PayUOlaMoneyParams
-dontwarn com.payu.olamoney.utils.PayUOlaMoneyPaymentParams
-dontwarn com.payu.ppiscanner.PayUQRScanner
-dontwarn com.payu.ppiscanner.PayUScannerConfig
-dontwarn com.payu.ppiscanner.interfaces.PayUScannerListener
-dontwarn com.phonepe.intent.sdk.api.TransactionRequest
-dontwarn com.phonepe.intent.sdk.api.TransactionRequestBuilder
-dontwarn org.bouncycastle.jsse.BCSSLParameters
-dontwarn org.bouncycastle.jsse.BCSSLSocket
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.slf4j.impl.StaticLoggerBinder

# ===== FLUTTER SPECIFIC RULES =====
# Keep Flutter engine classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-dontwarn io.flutter.**

# ===== PAYMENT GATEWAY RULES =====

# PayU SDK Rules
-keep class com.payu.** { *; }
-keep interface com.payu.** { *; }
-dontwarn com.payu.**

# PhonePe SDK Rules
-keep class com.phonepe.** { *; }
-keep interface com.phonepe.** { *; }
-dontwarn com.phonepe.**

# Cashfree SDK Rules
-keep class com.cashfree.** { *; }
-keep interface com.cashfree.** { *; }
-dontwarn com.cashfree.**

# ===== FIREBASE RULES =====
# Firebase Cloud Messaging
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Firebase Cloud Messaging only
-keep class com.google.firebase.messaging.** { *; }

# ===== GOOGLE PLAY SERVICES RULES =====
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# ===== GOOGLE MAPS SPECIFIC RULES =====
# Google Maps SDK
-keep class com.google.android.gms.maps.** { *; }
-keep interface com.google.android.gms.maps.** { *; }
-dontwarn com.google.android.gms.maps.**

# Google Maps Clustering (if using clustering)
-keep class com.google.maps.android.clustering.** { *; }
-dontwarn com.google.maps.android.clustering.**

# Google Directions API related classes
-keep class com.google.android.gms.location.** { *; }
-dontwarn com.google.android.gms.location.**

# ===== NETWORKING RULES =====
# OkHttp
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# Retrofit (if used)
-keep class retrofit2.** { *; }
-dontwarn retrofit2.**

# ===== SECURITY LIBRARIES =====
# BouncyCastle
-keep class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

# Conscrypt
-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

# ===== LOGGING RULES =====
# SLF4J
-keep class org.slf4j.** { *; }
-dontwarn org.slf4j.**

# ===== SERIALIZATION RULES =====
# Keep all model classes for JSON serialization
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Gson rules (if used)
-keep class com.google.gson.** { *; }
-dontwarn com.google.gson.**

# ===== GENERAL RULES =====
# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep annotations
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# ===== KOTLIN SPECIFIC RULES =====
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}

# ===== FLUTTER PLUGINS SPECIFIC RULES =====
# Google Maps Flutter Plugin
-keep class io.flutter.plugins.googlemaps.** { *; }
-dontwarn io.flutter.plugins.googlemaps.**

# Location Plugin
-keep class com.lyokone.location.** { *; }
-dontwarn com.lyokone.location.**

# Geolocator Plugin
-keep class com.baseflow.geolocator.** { *; }
-dontwarn com.baseflow.geolocator.**

# Permission Handler Plugin
-keep class com.baseflow.permissionhandler.** { *; }
-dontwarn com.baseflow.permissionhandler.**

# HTTP Plugin
-keep class io.flutter.plugins.urllauncher.** { *; }
-dontwarn io.flutter.plugins.urllauncher.**

# ===== ROUTE CALCULATION SPECIFIC RULES =====
# Keep classes that might be used for route calculation
-keep class * extends java.lang.Enum { *; }
-keep class * implements java.io.Serializable { *; }

# Keep classes with @JsonSerializable annotation (if using json_annotation)
-keep @interface com.google.gson.annotations.SerializedName
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# ===== ADDITIONAL SAFETY RULES =====
# Keep all classes in main package
-keep class com.eeil.ecoplug.** { *; }

# Prevent optimization of classes with native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep all public classes and their public and protected methods
-keep public class * {
    public protected *;
}

# ===== PERFORMANCE OPTIMIZATIONS =====
# Allow aggressive optimization
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify
