import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Simple test script to verify promo code API fixes
class SimplePromoCodeTester {
  static const String baseUrl = 'https://api2.eeil.online/api/v1';
  static const String verifyEndpoint = '$baseUrl/user/promocodes/verify';

  /// Main test function
  static Future<void> testPromoCodeFixes() async {
    print('🔔 ===== PROMO CODE API FIXES VERIFICATION =====');
    print('🔔 Testing corrected implementation...\n');

    // Test 1: Verify endpoint accepts POST with JSON body
    await testCorrectJsonFormat();

    // Test 2: Verify authentication requirement
    await testAuthenticationRequirement();

    // Test 3: Show what happens with real token (placeholder)
    await showRealTokenInstructions();

    print('\n🔔 ===== FIXES VERIFICATION COMPLETED =====');
  }

  /// Test 1: Verify the corrected JSON body format works
  static Future<void> testCorrectJsonFormat() async {
    print('🧪 TEST 1: Corrected JSON Body Format');
    print('=' * 50);

    final testCode = 'SAVE15';
    final payload = {'code': testCode};

    try {
      print('📤 Testing POST with JSON body: ${jsonEncode(payload)}');
      
      final response = await http.post(
        Uri.parse(verifyEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // No auth token - should get 401
        },
        body: jsonEncode(payload),
      ).timeout(Duration(seconds: 10));

      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body}');

      if (response.statusCode == 401) {
        print('✅ SUCCESS: Endpoint accepts JSON body format correctly');
        print('✅ SUCCESS: Authentication is properly required');
      } else if (response.statusCode == 405) {
        print('❌ FAILED: Endpoint still expects different method');
      } else if (response.statusCode == 400) {
        print('❌ FAILED: JSON body format might be incorrect');
      } else {
        print('⚠️  UNEXPECTED: Status ${response.statusCode}');
      }

    } catch (e) {
      print('❌ Error: $e');
    }
    print('');
  }

  /// Test 2: Verify authentication requirement
  static Future<void> testAuthenticationRequirement() async {
    print('🧪 TEST 2: Authentication Requirement');
    print('=' * 50);

    final payload = {'code': 'WELCOME10'};

    // Test with dummy token
    try {
      print('📤 Testing with dummy Bearer token...');
      
      final response = await http.post(
        Uri.parse(verifyEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer dummy_token_123',
        },
        body: jsonEncode(payload),
      ).timeout(Duration(seconds: 10));

      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body}');

      if (response.statusCode == 401) {
        print('✅ SUCCESS: Invalid tokens are properly rejected');
      } else if (response.statusCode == 200) {
        print('⚠️  UNEXPECTED: Dummy token was accepted (security issue?)');
      } else {
        print('⚠️  UNEXPECTED: Status ${response.statusCode}');
      }

    } catch (e) {
      print('❌ Error: $e');
    }
    print('');
  }

  /// Test 3: Show instructions for testing with real token
  static Future<void> showRealTokenInstructions() async {
    print('🧪 TEST 3: Real Token Testing Instructions');
    print('=' * 50);

    print('📝 To test with a real authentication token:');
    print('');
    print('1. 📱 Open the EcoPlug app');
    print('2. 🔐 Log in with valid credentials');
    print('3. 💾 The app will store the auth token in SharedPreferences');
    print('4. 🧪 Run this test with the token:');
    print('   dart test_promo_simple.dart YOUR_TOKEN_HERE');
    print('');
    print('📝 Expected behavior with valid token:');
    print('   - Status 200: Promo code verification works');
    print('   - Valid codes return success with credits/discount info');
    print('   - Invalid codes return error messages');
    print('');
    print('📝 The fixes implemented:');
    print('   ✅ Changed from query parameter to JSON body');
    print('   ✅ Added proper authentication token handling');
    print('   ✅ Added better error handling for different status codes');
    print('   ✅ Added debug logging for troubleshooting');
    print('');
  }

  /// Test with a real token if provided
  static Future<void> testWithRealToken(String authToken) async {
    print('🧪 BONUS TEST: Testing with Real Auth Token');
    print('=' * 50);

    final testCodes = ['SAVE15', 'WELCOME10', 'INVALID123'];

    for (final code in testCodes) {
      print('🎫 Testing promo code: $code');

      try {
        final payload = {'code': code};

        final response = await http.post(
          Uri.parse(verifyEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: jsonEncode(payload),
        ).timeout(Duration(seconds: 15));

        print('📊 Status: ${response.statusCode}');
        print('📥 Response: ${response.body}');

        if (response.statusCode == 200) {
          try {
            final responseData = jsonDecode(response.body);
            if (responseData['success'] == true) {
              print('✅ SUCCESS: $code is valid!');
              if (responseData['data'] != null) {
                final data = responseData['data'];
                print('💰 Credits: ${data['credits'] ?? 'N/A'}');
                print('💵 Min Amount: ${data['minimum_amount_applicable'] ?? 'N/A'}');
                print('📝 Description: ${data['description'] ?? 'N/A'}');
              }
            } else {
              print('❌ INVALID: ${responseData['message'] ?? 'Promo code not valid'}');
            }
          } catch (e) {
            print('❌ JSON Parse Error: $e');
          }
        } else if (response.statusCode == 401) {
          print('❌ AUTHENTICATION FAILED: Token is invalid or expired');
        } else {
          print('❌ UNEXPECTED STATUS: ${response.statusCode}');
        }

      } catch (e) {
        print('❌ Request error for $code: $e');
      }
      print('');
    }
  }
}

/// Main function
void main(List<String> args) async {
  await SimplePromoCodeTester.testPromoCodeFixes();

  // If a token is provided as command line argument, test with it
  if (args.isNotEmpty) {
    final token = args[0];
    print('\n🔑 Testing with provided auth token...');
    await SimplePromoCodeTester.testWithRealToken(token);
  }
}
