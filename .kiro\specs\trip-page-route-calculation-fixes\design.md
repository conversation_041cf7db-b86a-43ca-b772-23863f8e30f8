# Design Document

## Overview

This design addresses critical user experience issues in the trip page route calculation and station sheet display functionality. The solution implements immediate station sheet display, loading state management, maintained map interactivity during calculations, and automatic camera positioning for optimal route viewing. The design leverages the existing GoogleMapWidget and StationListSheet components while introducing new state management patterns for route calculations.

## Architecture

### Core Components

1. **TripPageController** - New state management controller for trip planning
2. **RouteCalculationService** - Service for handling route calculations with loading states
3. **StationSheetManager** - Enhanced manager for station sheet display and interactions
4. **CameraController** - Enhanced camera management for auto-zoom functionality
5. **LoadingStateManager** - Centralized loading state management

### Component Relationships

```mermaid
graph TD
    A[TripPage] --> B[TripPageController]
    B --> C[RouteCalculationService]
    B --> D[StationSheetManager]
    B --> E[GoogleMapWidget]
    D --> F[StationListSheet]
    C --> G[LoadingStateManager]
    E --> H[CameraController]
    
    C --> I[API Services]
    F --> J[Station Data]
    H --> K[Map Camera]
```

### State Flow

1. **Initial State**: User sets start and destination locations
2. **Immediate Response**: Station sheet displays immediately with available stations
3. **Background Processing**: Route calculation begins with loading indicators
4. **Interactive State**: Map remains responsive, station sheet accessible
5. **Completion State**: Route displayed, camera auto-zooms, stations updated

## Components and Interfaces

### TripPageController

```dart
class TripPageController extends StateNotifier<TripPageState> {
  final RouteCalculationService _routeService;
  final StationSheetManager _sheetManager;
  final CameraController _cameraController;
  
  // Core state management
  void setStartLocation(LatLng location);
  void setDestinationLocation(LatLng location);
  void calculateRoute();
  void onMapTap(LatLng position);
  
  // Station sheet management
  void showStationSheet();
  void hideStationSheet();
  void updateStationList(List<Station> stations);
  
  // Camera management
  void autoZoomToRoute(List<LatLng> routePoints);
  void focusOnLocation(LatLng location);
}
```

### RouteCalculationService

```dart
class RouteCalculationService {
  final LoadingStateManager _loadingManager;
  
  // Route calculation with loading states
  Future<RouteResult> calculateRoute({
    required LatLng start,
    required LatLng destination,
    required Function(RouteCalculationProgress) onProgress,
  });
  
  // Cancel ongoing calculations
  void cancelCalculation();
  
  // Retry failed calculations
  Future<RouteResult> retryCalculation();
}
```

### StationSheetManager

```dart
class StationSheetManager {
  final DraggableScrollableController _sheetController;
  
  // Immediate display management
  void showImmediately(List<Station> stations);
  void updateWithLoadingState();
  void updateWithRouteStations(List<Station> routeStations);
  
  // Interaction management
  void maintainInteractivity();
  void handleMapTapDuringCalculation(LatLng position);
  
  // Loading state display
  void showLoadingIndicator();
  void hideLoadingIndicator();
  void showErrorState(String error);
}
```

### Enhanced GoogleMapWidget Integration

```dart
// Extensions to existing GoogleMapWidget
extension TripPageMapWidget on GoogleMapWidget {
  // Auto-zoom functionality
  void autoZoomToPolyline(Set<Polyline> polylines);
  void fitBoundsWithPadding(LatLngBounds bounds, double padding);
  
  // Interactive state management
  void maintainInteractivityDuringCalculation();
  void handleTapDuringCalculation(LatLng position);
}
```

## Data Models

### TripPageState

```dart
class TripPageState {
  final LatLng? startLocation;
  final LatLng? destinationLocation;
  final bool isCalculatingRoute;
  final RouteCalculationProgress? calculationProgress;
  final List<Station> availableStations;
  final List<Station> routeStations;
  final Set<Polyline> routePolylines;
  final bool isStationSheetVisible;
  final StationSheetDisplayMode sheetMode;
  final String? errorMessage;
  final bool isMapInteractive;
}
```

### RouteCalculationProgress

```dart
class RouteCalculationProgress {
  final double percentage;
  final String currentStep;
  final Duration estimatedTimeRemaining;
  final bool isRetryable;
}
```

### StationSheetDisplayMode

```dart
enum StationSheetDisplayMode {
  immediate,      // Show immediately with all available stations
  loading,        // Show with loading indicator during calculation
  routeOptimized, // Show with route-optimized stations
  error          // Show error state with retry options
}
```

## Error Handling

### Route Calculation Failures

1. **Network Errors**: Display retry button with network status indicator
2. **Invalid Locations**: Show location validation errors with suggestions
3. **Service Unavailable**: Provide fallback options and estimated recovery time
4. **Timeout Errors**: Allow manual retry with extended timeout options

### Station Sheet Error States

1. **Loading Failures**: Graceful degradation to cached station data
2. **Empty Results**: Suggest expanding search radius or different locations
3. **API Errors**: Clear error messages with actionable next steps

### Map Interaction Errors

1. **Camera Animation Failures**: Fallback to current position without errors
2. **Polyline Rendering Issues**: Display route as markers if polyline fails
3. **Touch Event Conflicts**: Prioritize user interactions over automatic updates

## Testing Strategy

### Unit Tests

1. **TripPageController Tests**
   - State transitions during route calculation
   - Location setting and validation
   - Error handling scenarios

2. **RouteCalculationService Tests**
   - API integration with various response scenarios
   - Loading state management
   - Cancellation and retry logic

3. **StationSheetManager Tests**
   - Immediate display functionality
   - Loading state transitions
   - Interactive behavior during calculations

### Integration Tests

1. **End-to-End Trip Planning Flow**
   - Complete user journey from location setting to route display
   - Station sheet behavior throughout the process
   - Camera auto-zoom functionality

2. **Error Recovery Testing**
   - Network failure scenarios
   - Invalid input handling
   - Service degradation responses

3. **Performance Testing**
   - Route calculation response times
   - Station sheet rendering performance
   - Map interaction responsiveness

### Widget Tests

1. **Station Sheet Display Tests**
   - Immediate appearance when locations are set
   - Loading indicator display during calculations
   - Interactive elements remain functional

2. **Map Camera Tests**
   - Auto-zoom to route polylines
   - Smooth animation transitions
   - Bounds calculation accuracy

3. **Loading State Tests**
   - Visual feedback during calculations
   - Progress indicator accuracy
   - Error state display

## Implementation Phases

### Phase 1: Core State Management
- Implement TripPageController with basic state management
- Create RouteCalculationService with loading states
- Set up LoadingStateManager for centralized loading control

### Phase 2: Immediate Station Sheet Display
- Enhance StationSheetManager for immediate display
- Integrate with existing StationListSheet component
- Implement loading state indicators within the sheet

### Phase 3: Interactive Map During Calculation
- Maintain map tap functionality during route calculation
- Ensure station sheet remains accessible and responsive
- Handle concurrent user interactions and calculations

### Phase 4: Auto-zoom and Camera Management
- Implement automatic camera positioning after route calculation
- Add smooth animation transitions for route display
- Handle edge cases for very long or short routes

### Phase 5: Error Handling and Edge Cases
- Implement comprehensive error handling for all scenarios
- Add retry mechanisms and fallback options
- Ensure graceful degradation in failure cases

## Performance Considerations

### Route Calculation Optimization
- Implement request debouncing to prevent excessive API calls
- Use background processing to avoid blocking the UI thread
- Cache calculation results for similar routes

### Station Sheet Performance
- Lazy loading of station details to improve initial display speed
- Efficient list rendering for large numbers of stations
- Smooth animations without impacting map performance

### Map Rendering Optimization
- Efficient polyline rendering for complex routes
- Optimized marker clustering during route display
- Smooth camera animations without frame drops

## Accessibility Considerations

### Screen Reader Support
- Provide clear announcements for route calculation progress
- Describe station sheet state changes to screen readers
- Ensure all interactive elements have proper accessibility labels

### Visual Accessibility
- High contrast loading indicators for better visibility
- Clear visual hierarchy in station sheet during loading states
- Sufficient color contrast for all text and UI elements

### Motor Accessibility
- Large touch targets for all interactive elements
- Gesture alternatives for complex map interactions
- Timeout extensions for users who need more time