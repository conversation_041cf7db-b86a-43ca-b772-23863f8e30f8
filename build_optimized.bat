@echo off
echo 🚀 Building Optimized EcoPlug App
echo ================================

echo.
echo 📦 Step 1: Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo ❌ Flutter clean failed
    exit /b 1
)

echo.
echo 📥 Step 2: Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Flutter pub get failed
    exit /b 1
)

echo.
echo 🔧 Step 3: Building optimized App Bundle (recommended for Google Play)...
call flutter build appbundle --release --shrink --obfuscate --split-debug-info=build/debug-info
if %errorlevel% neq 0 (
    echo ❌ App Bundle build failed
    exit /b 1
)

echo.
echo 📱 Step 4: Building optimized APK (for testing)...
call flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info --split-per-abi
if %errorlevel% neq 0 (
    echo ❌ APK build failed
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.
echo 📊 Build artifacts:
echo   📦 App Bundle: build\app\outputs\bundle\release\app-release.aab
echo   📱 APKs: build\app\outputs\flutter-apk\
echo.
echo 💡 Tips:
echo   - Upload the .aab file to Google Play Store (smaller downloads)
echo   - Use APKs for testing on specific devices
echo   - App Bundle automatically optimizes for each device
echo.
echo 🎉 Ready for deployment!
pause
