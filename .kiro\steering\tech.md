# Technology Stack

## Framework & Language
- **Flutter**: Cross-platform mobile development framework
- **Dart**: Programming language (SDK >=3.0.0 <4.0.0)
- **Target Platforms**: Android, iOS, Web

## State Management
- **Flutter Riverpod**: Primary state management solution with providers
- **Provider**: Legacy state management (being migrated to Riverpod)

## Backend & Services
- **Firebase**: Core backend services
  - Firebase Core
  - Firebase Cloud Messaging (FCM) for push notifications
  - Firebase Authentication integration
- **REST APIs**: HTTP-based API communication using Dio and HTTP packages

## Key Dependencies

### UI & Navigation
- `google_nav_bar`: Bottom navigation
- `line_icons`: Icon library
- `font_awesome_flutter`: Additional icons
- `flutter_animate`: Animations
- `shimmer`: Loading animations
- `lottie`: Complex animations

### Maps & Location
- `google_maps_flutter`: Google Maps integration
- `google_polyline_algorithm`: Route polylines
- `geolocator`: Location services

### Payment Gateways
- `phonepe_payment_sdk`: PhonePe integration
- `payu_checkoutpro_flutter`: PayU integration
- `flutter_cashfree_pg_sdk`: Cashfree integration

### Storage & Persistence
- `flutter_secure_storage`: Secure credential storage
- `shared_preferences`: App preferences
- `path_provider`: File system access

### Networking & Connectivity
- `dio`: HTTP client with interceptors
- `connectivity_plus`: Network connectivity monitoring
- `internet_connection_checker`: Connection validation
- `retry`: Network retry logic

### Notifications
- `flutter_local_notifications`: Local notifications
- `timezone`: Timezone handling for notifications

## Build Commands

### Development
```bash
# Run in debug mode
flutter run

# Run with specific flavor/environment
flutter run --flavor dev
flutter run --flavor prod

# Hot reload during development
# Press 'r' in terminal or use IDE hot reload
```

### Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/specific_test.dart

# Run tests with coverage
flutter test --coverage
```

### Building
```bash
# Build APK for Android
flutter build apk --release

# Build App Bundle for Google Play
flutter build appbundle --release

# Build for iOS
flutter build ios --release

# Build for web
flutter build web --release
```

### Code Generation
```bash
# Generate Riverpod providers and Freezed models
flutter packages pub run build_runner build

# Watch for changes and auto-generate
flutter packages pub run build_runner watch

# Clean generated files
flutter packages pub run build_runner clean
```

### Dependency Management
```bash
# Get dependencies
flutter pub get

# Upgrade dependencies
flutter pub upgrade

# Analyze code
flutter analyze

# Format code
flutter format .
```

## Development Tools
- **Custom Lint**: Code quality with Riverpod-specific linting
- **Flutter Lints**: Standard Flutter linting rules
- **Build Runner**: Code generation for providers and models
- **Mockito**: Testing framework for mocking dependencies