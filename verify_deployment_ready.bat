@echo off
echo 🔍 EcoPlug Deployment Verification Script
echo ==========================================
echo.

echo 📋 Checking for restricted permissions that cause Google Play issues...
echo.

echo 🔍 Step 1: Checking AndroidManifest.xml for restricted permissions...
findstr /i "MANAGE_EXTERNAL_STORAGE\|FOREGROUND_SERVICE_DATA_SYNC\|USE_FULL_SCREEN_INTENT\|FOREGROUND_SERVICE" android\app\src\main\AndroidManifest.xml
if %errorlevel% equ 0 (
    echo ❌ FOUND RESTRICTED PERMISSIONS! These will cause Google Play rejection.
    echo Please remove these permissions before deployment.
    pause
    exit /b 1
) else (
    echo ✅ No restricted permissions found in AndroidManifest.xml
)

echo.
echo 🔍 Step 2: Checking for storage permissions...
findstr /i "READ_EXTERNAL_STORAGE\|WRITE_EXTERNAL_STORAGE\|READ_MEDIA_IMAGES\|READ_MEDIA_VIDEO\|CAMERA" android\app\src\main\AndroidManifest.xml
if %errorlevel% equ 0 (
    echo ❌ FOUND STORAGE PERMISSIONS! These were requested to be removed.
    echo Please verify these are intentional or remove them.
    pause
    exit /b 1
) else (
    echo ✅ No storage permissions found in AndroidManifest.xml
)

echo.
echo 🔍 Step 3: Listing all current permissions...
echo Current permissions in your app:
findstr /i "uses-permission" android\app\src\main\AndroidManifest.xml
echo.

echo 🔍 Step 4: Checking for removed service declarations...
findstr /i "ChargingBackgroundService\|CustomChargingNotificationHandler" android\app\src\main\AndroidManifest.xml
if %errorlevel% equ 0 (
    echo ❌ FOUND REMOVED SERVICES! These should have been deleted.
    pause
    exit /b 1
) else (
    echo ✅ No removed services found in AndroidManifest.xml
)

echo.
echo 🔍 Step 5: Checking if removed service files still exist...
if exist "android\app\src\main\kotlin\com\eeil\ecoplug\ChargingBackgroundService.kt" (
    echo ❌ ChargingBackgroundService.kt still exists! Should be deleted.
    pause
    exit /b 1
) else (
    echo ✅ ChargingBackgroundService.kt properly removed
)

if exist "android\app\src\main\kotlin\com\eeil\ecoplug\CustomChargingNotificationHandler.kt" (
    echo ❌ CustomChargingNotificationHandler.kt still exists! Should be deleted.
    pause
    exit /b 1
) else (
    echo ✅ CustomChargingNotificationHandler.kt properly removed
)

echo.
echo 🔍 Step 6: Checking build configuration...
findstr /i "android.enableBuildCache" android\gradle.properties
if %errorlevel% equ 0 (
    echo ❌ FOUND DEPRECATED PROPERTY! android.enableBuildCache should be removed.
    pause
    exit /b 1
) else (
    echo ✅ No deprecated build properties found
)

echo.
echo 🎉 VERIFICATION COMPLETE!
echo ========================
echo.
echo ✅ All restricted permissions removed
echo ✅ All storage permissions removed  
echo ✅ All foreground services removed
echo ✅ All deprecated properties removed
echo ✅ Build configuration optimized
echo.
echo 🚀 YOUR APP IS READY FOR GOOGLE PLAY DEPLOYMENT!
echo.
echo 📋 Final deployment checklist:
echo   1. Build optimized App Bundle: flutter build appbundle --release --shrink --obfuscate
echo   2. Test on real devices to ensure functionality works
echo   3. Upload .aab file to Google Play Console
echo   4. No restricted permission warnings should appear
echo.
echo 🎯 Expected Google Play Console result:
echo   ✅ No permission warnings
echo   ✅ No restricted permission errors
echo   ✅ Automatic approval for standard permissions
echo   ✅ Smaller APK size with optimizations
echo.
pause
