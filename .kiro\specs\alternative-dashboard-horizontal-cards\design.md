# Design Document

## Overview

The alternative dashboard with horizontal cards provides a modern, swipe-based interface for browsing charging stations. This design replaces the traditional vertical bottom sheet with horizontally scrolling station cards while maintaining all existing functionality and performance optimizations.

## Architecture

### Component Structure

```
DashboardHorizontalCards
├── SearchHeader (search bar + filter icons)
├── GoogleMapWidget (existing map component)
├── HorizontalStationCards (new PageView-based component)
│   ├── StationCard (individual card component)
│   └── PageIndicator (dots showing current position)
└── FloatingActionButtons (location, refresh, etc.)
``` in the station detail page , below bottom side start charging indication button ,is not responsively adjust accross the device display ranged , yester day i see the start or instant charging text is , overlayer and goes outside of the box fix this make sure it should not goes ouside of box the text lable 

### State Management Integration

The alternative dashboard will integrate with the existing `DashboardNotifier` and `DashboardState` to maintain consistency:

- Reuse existing `dashboardNotifierProvider` for station data
- Leverage cached station data from `formattedNearestStations` in the station detail page , below bottom side start charging indication button ,is not responsively adjust accross the device display ranged , yester day i see the start or instant charging text is , overlayer and goes outside of the box fix this make sure it should not goes ouside of box the text lable  in the station detail page , below bottom side start charging indication button ,is not responsively adjust accross the device display ranged , yester day i see the start or instant charging text is , overlayer and goes outside of the box fix this make sure it should not goes ouside of box the text lable 
- Maintain existing performance optimizations and API calls
- Preserve map state management through `lastCameraPosition`

## Components and Interfaces

### 1. DashboardHorizontalCards (Main Component)

**Purpose**: Main dashboard screen with horizontal card layout

**Key Properties**:
- Extends `ConsumerStatefulWidget` for Riverpod integration
- Maintains existing animation controllers for smooth transitions
- Preserves all current dashboard functionality

**State Variables**:
```dart
class DashboardHorizontalCardsState extends ConsumerStatefulWidget {
  // Existing dashboard state variables
  PageController _pageController;
  int _currentStationIndex = 0;
  bool _isSearchExpanded = false;
  
  // Animation controllers (reuse existing ones)
  AnimationController _searchAnimationController;
  Animation<double> _searchAnimation;
}
```

### 2. SearchHeader Component

**Purpose**: Top section with search bar and filter functionality

**Design Specifications**:
- Fixed position at top of screen
- Search icon that expands horizontally when tapped
- Filter icon positioned to the right of search
- Smooth animation transition to `station_list_page.dart`

**Animation Behavior**:
```dart
// Search expansion animation
void _expandSearchAndNavigate() {
  _searchAnimationController.forward().then((_) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, _) => StationListPage(),
        transitionsBuilder: (context, animation, _, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  });
}
```

### 3. HorizontalStationCards Component

**Purpose**: Horizontally scrolling station cards using PageView

**Key Features**:
- `PageView.builder` for smooth horizontal scrolling
- Snap-to-card behavior for precise navigation
- Page indicator dots showing current position
- Automatic map focus when card changes

**Implementation Structure**:
```dart
class HorizontalStationCards extends StatelessWidget {
  final List<Map<String, dynamic>> stations;
  final PageController pageController;
  final Function(int) onPageChanged;
  final Function(Map<String, dynamic>) onStationTapped;

  Widget build(BuildContext context) {
    return Column(
      children: [
        // PageView for horizontal scrolling
        Expanded(
          child: PageView.builder(
            controller: pageController,
            onPageChanged: onPageChanged,
            itemCount: stations.length,
            itemBuilder: (context, index) {
              return StationCard(
                station: stations[index],
                onTap: () => onStationTapped(stations[index]),
              );
            },
          ),
        ),
        // Page indicator
        PageIndicator(
          currentIndex: currentIndex,
          totalPages: stations.length,
        ),
      ],
    );
  }
}
```

### 4. StationCard Component

**Purpose**: Individual station card with modern Material Design

**Design Specifications**:
- Card elevation and rounded corners following Material Design 3
- Station name, address, distance, and status
- Connector icons displayed from API data (`types` field)
- Status indicator with appropriate colors
- Tap gesture for navigation to coordinates

**Visual Layout**:
```
┌─────────────────────────────────────┐
│  Station Name               [Status]│
│  📍 Address, City                   │
│  🚗 2.5 km away                     │
│                                     │
│  Connectors: [🔌] [⚡] [🔋]         │
│                                     │
│  [Tap to navigate to location]      │
└─────────────────────────────────────┘
```

**Connector Icon Implementation**:
```dart
Widget _buildConnectorIcons(List<dynamic>? types) {
  if (types == null || types.isEmpty) return SizedBox.shrink();
  
  return Wrap(
    spacing: 8.0,
    children: types.map((type) {
      final iconUrl = type['icon'] ?? _getDefaultConnectorIcon(type['name']);
      return Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Image.network(
          iconUrl,
          width: 24,
          height: 24,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.electrical_services, size: 20);
          },
        ),
      );
    }).toList(),
  );
}
```

## Data Models

### Station Data Structure

The component will use the existing station data format from the nearest stations API:

```dart
Map<String, dynamic> stationData = {
  'id': String,
  'name': String,
  'latitude': double,
  'longitude': double,
  'address': String,
  'city': String?,
  'uid': String,
  'distance': double,
  'status': String,
  'availability': String,
  'connectorType': String,
  'types': List<dynamic>, // Contains connector information with icons
  'mapPinUrl': String,
  'focusedMapPinUrl': String,
};
```

### Connector Type Structure

From the API's `types` field:
```dart
{
  'name': String, // e.g., "CCS2", "Type 2 AC"
  'icon': String, // URL to connector icon image
  'power': String?, // Power output if available
}
```

## Error Handling

### No Stations Available
- Display empty state with helpful message
- Provide refresh button to retry loading
- Maintain search functionality even when no stations are shown

### Network Connectivity Issues
- Integrate with existing `ConnectivityMonitor`
- Show offline indicator when network is unavailable
- Cache last known station data for offline viewing

### API Failures
- Graceful fallback to cached data
- Error messages with retry options
- Maintain map functionality even if station data fails

## Testing Strategy

### Unit Tests
- Test station card rendering with various data states
- Verify connector icon loading and fallback behavior
- Test page controller navigation and state management

### Widget Tests
- Test horizontal scrolling behavior
- Verify search animation and navigation
- Test station card tap interactions

### Integration Tests
- Test full dashboard flow with real API data
- Verify map integration and coordinate navigation
- Test performance with large station datasets

### Performance Tests
- Measure PageView scrolling performance
- Test image loading optimization for connector icons
- Verify memory usage with multiple station cards

## Implementation Phases

### Phase 1: Core Structure
- Create `DashboardHorizontalCards` main component
- Implement basic PageView with station cards
- Set up state management integration

### Phase 2: Station Cards
- Design and implement `StationCard` component
- Add connector icon display from API data
- Implement status indicators and styling

### Phase 3: Search Integration
- Create `SearchHeader` component
- Implement search animation and navigation
- Integrate with existing `StationListPage`

### Phase 4: Map Integration
- Connect horizontal cards to map focus
- Implement station selection and navigation
- Preserve existing map performance optimizations

### Phase 5: Polish and Testing
- Add page indicators and smooth animations
- Implement error handling and empty states
- Comprehensive testing and performance optimization

## Dashboard Switching Mechanism

To enable A/B testing and easy switching between dashboard variants:

```dart
// In main app routing or dashboard selection
enum DashboardType { vertical, horizontal }

class DashboardSelector extends StatelessWidget {
  final DashboardType type;
  
  Widget build(BuildContext context) {
    switch (type) {
      case DashboardType.vertical:
        return DashboardScreen(); // Existing dashboard
      case DashboardType.horizontal:
        return DashboardHorizontalCards(); // New dashboard
    }
  }
}
```

This design ensures both dashboards can coexist while sharing the same underlying data and state management systems.