name: ecoplug
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'  # Modified to be more flexible with SDK versions

dependencies:
  flutter:
    sdk: flutter
  flutter_secure_storage: ^9.0.0
  percent_indicator: ^4.2.5
  cached_network_image: ^3.3.0
  lottie: ^3.3.1
  geolocator: ^14.0.0
  http: ^1.1.0  # Updated to match flutter_map's requirement
  webview_flutter: ^4.10.0
  google_nav_bar: ^5.0.6
  line_icons: ^2.0.3
  google_maps_flutter: ^2.5.0
  google_polyline_algorithm: ^3.1.0
  cupertino_icons: ^1.0.2
  webview_flutter_plus: ^0.4.12
  webview_flutter_android: ^4.3.4  # Updated to compatible version
  url_launcher: ^6.1.14
  font_awesome_flutter: ^10.5.0
  intl: ^0.20.2
  shimmer: ^3.0.0
  image_picker: ^1.1.2
  shared_preferences: ^2.5.3
  sms_autofill: ^2.4.1
  jwt_decoder: ^2.0.1
  connectivity_plus: ^6.1.4  # Latest version with improved performance and features
  dio: ^5.4.1
  retry: ^3.1.2
  provider: ^6.1.1
  flutter_riverpod: ^2.6.1 # Add Riverpod
  riverpod_annotation: ^2.6.1 # Add Riverpod annotation
  device_info_plus: ^9.1.2  # For collecting device information
  package_info_plus: ^8.3.0  # Upgraded as per suggestion
  internet_connection_checker: ^1.0.0+1  # For more reliable connection checking
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  collection: ^1.18.0
  # Core dependencies
  crypto: ^3.0.6
  uuid: ^4.5.1
  flutter_svg: ^2.1.0
  flutter_staggered_animations: ^1.1.1
  flutter_inappwebview: ^6.1.5
  path_provider: ^2.1.2
  open_file: ^3.3.2
  permission_handler: ^11.3.1
  share_plus: ^11.0.0
  fl_chart: ^0.69.0  # For analytics charts and graphs
  flutter_animate: ^4.5.2
  flutter_local_notifications: ^17.2.2  # For Android system notifications
  timezone: ^0.9.2  # For scheduled notifications with timezone support
  phonepe_payment_sdk: ^3.0.0  # PhonePe Payment Gateway SDK
  payu_checkoutpro_flutter: ^1.3.1  # PayU Payment Gateway SDK
  liquid_glass_renderer: ^0.1.1-dev.9  # Liquid glass effect for navigation bar and wallet components

  # Firebase Cloud Messaging dependencies
  firebase_core: ^3.6.0  # Firebase core functionality
  firebase_messaging: ^15.1.3  # Firebase Cloud Messaging
  flutter_cashfree_pg_sdk: 2.2.8+46

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.2
  build_runner: ^2.4.15
  freezed: ^2.4.5
  json_serializable: ^6.7.1
  custom_lint: ^0.7.3 # Add custom_lint for Riverpod linting
  riverpod_generator: ^2.6.1 # Add Riverpod generator
  riverpod_lint: ^2.6.4
  flutter_launcher_icons: ^0.14.1 # Modern app icon generation

flutter:
  uses-material-design: true
  assets:
    - assets/images/ecoplug_logo.png
    - assets/images/ecoplug_logo_dark.png
    - assets/images/ecoplug_logo_dark_fixed.png
    - assets/images/charging_session _screen_background.png
    # Removed local connector icon assets to ensure API-only usage

    - assets/images/india_flag.png
    - assets/data/erorr Animation.json
    - assets/data/thunder_animation.json
    - assets/RFID_CARDS/RFIDCARDS_1.png
    - assets/RFID_CARDS/RFIDCARDS_2.png
    - assets/markers/
    - assets/icons/cars/car marker icons.png
    - assets/icons/cars/car_marker.svg

# Modern App Icon Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/ecoplug_logo_dark.png"
  min_sdk_android: 21 # Android 5.0+
  remove_alpha_ios: true # Remove alpha channel for App Store compliance

  # Modern Adaptive Icons for Android (API 26+)
  adaptive_icon_background: "#121212" # Dark background to match app theme
  adaptive_icon_foreground: "assets/images/ecoplug_logo_dark.png"

  # Themed Icons for Android 13+ (Supports Material You)
  adaptive_icon_monochrome: "assets/images/ecoplug_logo_dark.png"

  # Platform-specific configurations
  android_config:
    generate_round_icon: true # Generate round icons for Android

  ios_config:
    generate_precomposed_icons: true # Generate precomposed icons for iOS

  # Web support (if needed)
  web:
    generate: true
    image_path: "assets/images/ecoplug_logo.png"
    background_color: "#FFFFFF"
    theme_color: "#67C44C"
