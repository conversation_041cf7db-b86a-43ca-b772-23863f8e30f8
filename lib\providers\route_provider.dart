import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../models/route_model.dart';
import '../models/destination_station.dart';
import '../models/route_alternative.dart';
import '../services/directions_service.dart';
import '../services/destination_locations_service.dart';
import '../core/api/api_service.dart';

/// Provider for DirectionsService
final directionsServiceProvider = Provider<DirectionsService>((ref) {
  return DirectionsService();
});

/// Provider for DestinationLocationsService
final destinationLocationsServiceProvider =
    Provider<DestinationLocationsService>((ref) {
  return DestinationLocationsService(ApiService());
});

/// State class for route calculation
class RouteState {
  final RouteModel? route;
  final bool isLoading;
  final String? error;
  final LatLng? origin;
  final LatLng? destination;
  final List<DestinationStation> destinationStations;
  final bool isLoadingStations;
  final String? stationsError;
  final RouteAlternatives? routeAlternatives;
  final bool isLoadingAlternatives;
  final String? selectedRouteId;

  const RouteState({
    this.route,
    this.isLoading = false,
    this.error,
    this.origin,
    this.destination,
    this.destinationStations = const [],
    this.isLoadingStations = false,
    this.stationsError,
    this.routeAlternatives,
    this.isLoadingAlternatives = false,
    this.selectedRouteId,
  });

  RouteState copyWith({
    RouteModel? route,
    bool? isLoading,
    String? error,
    LatLng? origin,
    LatLng? destination,
    List<DestinationStation>? destinationStations,
    bool? isLoadingStations,
    String? stationsError,
    RouteAlternatives? routeAlternatives,
    bool? isLoadingAlternatives,
    String? selectedRouteId,
  }) {
    return RouteState(
      route: route ?? this.route, // Preserve existing route when null
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error, // Preserve existing error when null
      origin: origin ?? this.origin,
      destination: destination ?? this.destination,
      destinationStations: destinationStations ?? this.destinationStations,
      isLoadingStations: isLoadingStations ?? this.isLoadingStations,
      stationsError: stationsError ??
          this.stationsError, // Preserve existing stationsError when null
      routeAlternatives: routeAlternatives ??
          this.routeAlternatives, // Preserve existing routeAlternatives when null
      isLoadingAlternatives:
          isLoadingAlternatives ?? this.isLoadingAlternatives,
      selectedRouteId: selectedRouteId ??
          this.selectedRouteId, // Preserve existing selectedRouteId when null
    );
  }

  /// Check if we have both origin and destination
  bool get hasValidLocations => origin != null && destination != null;

  /// Check if route is available
  bool get hasRoute => currentRoute != null;

  /// Get polyline points for map display
  List<LatLng> get polylinePoints => currentRoute?.polylinePoints ?? [];

  /// Get route bounds for camera fitting
  LatLngBounds? get routeBounds => currentRoute?.bounds;

  /// Check if we have destination stations
  bool get hasDestinationStations => destinationStations.isNotEmpty;

  /// Check if we have route alternatives
  bool get hasRouteAlternatives =>
      routeAlternatives != null && routeAlternatives!.hasMultipleAlternatives;

  /// Get the currently selected route alternative
  RouteAlternative? get selectedRouteAlternative {
    if (routeAlternatives == null) return null;
    if (selectedRouteId != null) {
      return routeAlternatives!.getRouteById(selectedRouteId!);
    }
    return routeAlternatives!.currentRoute;
  }

  /// Get the current route (either from alternatives or direct route)
  RouteModel? get currentRoute {
    if (selectedRouteAlternative != null) {
      return selectedRouteAlternative!.route;
    }
    return route;
  }

  @override
  String toString() {
    return 'RouteState(hasRoute: $hasRoute, isLoading: $isLoading, error: $error, stations: ${destinationStations.length}, alternatives: ${routeAlternatives?.alternatives.length ?? 0})';
  }
}

/// Notifier for managing route state
class RouteNotifier extends StateNotifier<RouteState> {
  final DirectionsService _directionsService;
  final DestinationLocationsService _destinationLocationsService;

  RouteNotifier(this._directionsService, this._destinationLocationsService)
      : super(const RouteState());

  /// Calculate route between two points
  Future<void> calculateRoute({
    required LatLng origin,
    required LatLng destination,
    String travelMode = 'driving',
    bool avoidTolls = false,
    bool avoidHighways = false,
    bool avoidFerries = false,
  }) async {
    // Don't calculate if same locations
    if (state.origin == origin &&
        state.destination == destination &&
        state.hasRoute) {
      debugPrint('🔄 ROUTE: Same locations, skipping calculation');
      return;
    }

    debugPrint('🗺️ ROUTE: Calculating route from $origin to $destination');

    // Set loading state
    state = state.copyWith(
      isLoading: true,
      error: null,
      origin: origin,
      destination: destination,
    );

    try {
      final route = await _directionsService.getDirections(
        origin: origin,
        destination: destination,
        travelMode: travelMode,
        avoidTolls: avoidTolls,
        avoidHighways: avoidHighways,
        avoidFerries: avoidFerries,
      );

      if (route != null) {
        // Validate route data before setting state
        if (route.polylinePoints.isEmpty) {
          debugPrint(
              '❌ ROUTE: Route calculated but no polyline points available for origin: $origin, destination: $destination');
          state = state.copyWith(
            isLoading: false,
            error: 'Route calculated but no polyline data available',
          );
          return;
        }

        // Validate coordinate ranges
        final latitudes = route.polylinePoints.map((p) => p.latitude);
        final longitudes = route.polylinePoints.map((p) => p.longitude);
        final minLat = latitudes.reduce((a, b) => a < b ? a : b);
        final maxLat = latitudes.reduce((a, b) => a > b ? a : b);
        final minLng = longitudes.reduce((a, b) => a < b ? a : b);
        final maxLng = longitudes.reduce((a, b) => a > b ? a : b);

        final validCoords =
            minLat >= -90 && maxLat <= 90 && minLng >= -180 && maxLng <= 180;
        if (!validCoords) {
          debugPrint('❌ ROUTE: Invalid coordinate ranges detected');
          debugPrint('❌ ROUTE: Lat range: $minLat to $maxLat');
          debugPrint('❌ ROUTE: Lng range: $minLng to $maxLng');
          state = state.copyWith(
            isLoading: false,
            error: 'Invalid route coordinates detected',
          );
          return;
        }

        state = state.copyWith(
          route: route,
          isLoading: false,
          error: null,
        );

        debugPrint('✅ ROUTE: Successfully calculated and validated route');
        debugPrint(
            '✅ ROUTE: Route has ${route.polylinePoints.length} polyline points');
        debugPrint(
            '✅ ROUTE: Route distance: ${route.distance}, duration: ${route.duration}');
        debugPrint(
            '✅ ROUTE: Coordinate ranges - Lat: $minLat to $maxLat, Lng: $minLng to $maxLng');
        debugPrint('✅ ROUTE: Route bounds: ${route.bounds}');

        // Automatically fetch destination stations along the route
        _fetchDestinationStations(route.polylinePoints);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to calculate route - directions service returned null',
        );
        debugPrint(
            '❌ ROUTE: Failed to calculate route for origin: $origin, destination: $destination - directions service returned null');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error:
            'Error calculating route: $e for origin: $origin, destination: $destination',
      );
      debugPrint(
          '❌ ROUTE: Exception during calculation for origin: $origin, destination: $destination: $e');
    }
  }

  /// Calculate route with waypoints
  Future<void> calculateRouteWithWaypoints({
    required LatLng origin,
    required LatLng destination,
    required List<LatLng> waypoints,
    String travelMode = 'driving',
    bool optimizeWaypoints = true,
  }) async {
    debugPrint(
        '🗺️ ROUTE: Calculating route with ${waypoints.length} waypoints');

    state = state.copyWith(
      isLoading: true,
      error: null,
      origin: origin,
      destination: destination,
    );

    try {
      final route = await _directionsService.getDirectionsWithWaypoints(
        origin: origin,
        destination: destination,
        waypoints: waypoints,
        travelMode: travelMode,
        optimizeWaypoints: optimizeWaypoints,
      );

      if (route != null) {
        state = state.copyWith(
          route: route,
          isLoading: false,
          error: null,
        );
        debugPrint('✅ ROUTE: Successfully calculated waypoint route');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to calculate route with waypoints',
        );
        debugPrint('❌ ROUTE: Failed to calculate waypoint route');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error calculating waypoint route: $e',
      );
      debugPrint('❌ ROUTE: Exception during waypoint calculation: $e');
    }
  }

  /// Clear current route
  void clearRoute() {
    debugPrint('🗑️ ROUTE: Clearing current route');
    state = const RouteState();
  }

  /// Update origin location
  void updateOrigin(LatLng origin) {
    state = state.copyWith(origin: origin);

    // Auto-calculate if we have destination
    if (state.destination != null) {
      calculateRoute(
        origin: origin,
        destination: state.destination!,
      );
    }
  }

  /// Update destination location
  void updateDestination(LatLng destination) {
    state = state.copyWith(destination: destination);

    // Auto-calculate if we have origin
    if (state.origin != null) {
      calculateRoute(
        origin: state.origin!,
        destination: destination,
      );
    }
  }

  /// Update both locations and calculate route
  void updateLocations({
    required LatLng origin,
    required LatLng destination,
  }) {
    calculateRoute(
      origin: origin,
      destination: destination,
    );
  }

  /// Retry route calculation
  void retryCalculation() {
    if (state.hasValidLocations) {
      calculateRoute(
        origin: state.origin!,
        destination: state.destination!,
      );
    }
  }

  /// Fetch charging stations along the route
  Future<void> _fetchDestinationStations(List<LatLng> polylinePoints) async {
    if (polylinePoints.isEmpty) {
      debugPrint('❌ ROUTE: No polyline points to fetch stations');
      return;
    }

    debugPrint('🏪 ROUTE: Fetching charging stations along route');
    debugPrint('🗺️ ROUTE: Route has ${polylinePoints.length} polyline points');

    // Set loading state for stations
    state = state.copyWith(
      isLoadingStations: true,
      stationsError: null,
    );

    try {
      // COMPREHENSIVE API CALL DEBUGGING - SHOW ALL PAYLOAD DATA
      if (kDebugMode) {
        debugPrint('\n🚀 === DESTINATION STATIONS API CALL DEBUG ===');
        debugPrint(
            '🌐 SERVICE: DestinationLocationsService.getStationsAlongRouteSampled');
        debugPrint('📦 POLYLINE POINTS COUNT: ${polylinePoints.length}');
        debugPrint('📦 SAMPLE RATE: 5 (every 5th point)');
        debugPrint(
            '📦 ESTIMATED SAMPLED POINTS: ${(polylinePoints.length / 5).ceil()}');
        debugPrint('📦 FIRST 10 POLYLINE POINTS:');
        for (int i = 0; i < polylinePoints.length && i < 10; i++) {
          final point = polylinePoints[i];
          debugPrint('   [$i]: lat=${point.latitude}, lng=${point.longitude}');
        }
        if (polylinePoints.length > 10) {
          debugPrint('   ... and ${polylinePoints.length - 10} more points');
        }
        debugPrint('📦 LAST 5 POLYLINE POINTS:');
        final startIndex =
            polylinePoints.length > 5 ? polylinePoints.length - 5 : 0;
        for (int i = startIndex; i < polylinePoints.length; i++) {
          final point = polylinePoints[i];
          debugPrint('   [$i]: lat=${point.latitude}, lng=${point.longitude}');
        }
        debugPrint('🚀 === END DESTINATION STATIONS API CALL DEBUG ===\n');
      }

      final stations =
          await _destinationLocationsService.getStationsAlongRouteSampled(
        polylinePoints,
        sampleRate: 5, // Sample every 5th point for performance
      );

      // Enhanced debugging for marker creation
      if (kDebugMode) {
        debugPrint('🎯 ROUTE: === MARKER CREATION DEBUG ===');
        debugPrint('🎯 Stations received: ${stations.length}');

        for (int i = 0; i < stations.length; i++) {
          final station = stations[i];
          debugPrint('🎯 Marker ${i + 1} for Station: ${station.name}');
          debugPrint(
              '   📍 Coordinates: (${station.latitude}, ${station.longitude})');
          debugPrint('   🏷️ ID: ${station.id}');
          debugPrint('   📊 Status: ${station.status}');
          debugPrint('   🔌 Connector: ${station.connectorType ?? 'N/A'}');

          // Validate coordinates
          if (station.latitude == 0.0 && station.longitude == 0.0) {
            debugPrint('   ⚠️ WARNING: Station has zero coordinates!');
          } else if (station.latitude < -90 ||
              station.latitude > 90 ||
              station.longitude < -180 ||
              station.longitude > 180) {
            debugPrint('   ⚠️ WARNING: Station has invalid coordinates!');
          } else {
            debugPrint('   ✅ Coordinates are valid');
          }
        }
        debugPrint('🎯 === END MARKER CREATION DEBUG ===');
      }

      state = state.copyWith(
        destinationStations: stations,
        isLoadingStations: false,
        stationsError: null,
      );

      debugPrint(
          '✅ ROUTE: Found ${stations.length} charging stations along route');
      debugPrint(
          '🗺️ ROUTE: Stations will be converted to map markers for display');
    } catch (e) {
      state = state.copyWith(
        isLoadingStations: false,
        stationsError: 'Failed to fetch charging stations: $e',
      );
      debugPrint('❌ ROUTE: Error fetching destination stations: $e');
    }
  }

  /// Manually fetch destination stations for current route
  Future<void> fetchDestinationStations() async {
    if (state.hasRoute) {
      await _fetchDestinationStations(state.polylinePoints);
    } else {
      debugPrint('❌ ROUTE: No route available to fetch stations');
    }
  }

  /// Clear destination stations
  void clearDestinationStations() {
    state = state.copyWith(
      destinationStations: [],
      isLoadingStations: false,
      stationsError: null,
    );
  }

  /// Calculate route alternatives between two points
  Future<void> calculateRouteAlternatives({
    required LatLng origin,
    required LatLng destination,
  }) async {
    debugPrint('🛣️ ROUTE: === CALCULATING ROUTE ALTERNATIVES ===');
    debugPrint('🛣️ ROUTE: Origin: $origin');
    debugPrint('🛣️ ROUTE: Destination: $destination');

    // Set loading state
    state = state.copyWith(
      isLoading: true,
      isLoadingAlternatives: true,
      error: null,
      origin: origin,
      destination: destination,
    );

    debugPrint(
        '🛣️ ROUTE: State updated to loading, calling directions service...');

    try {
      final alternatives = await _directionsService.getRouteAlternatives(
        origin: origin,
        destination: destination,
      );

      debugPrint(
          '🛣️ ROUTE: Directions service returned: ${alternatives != null ? 'SUCCESS' : 'NULL'}');

      if (alternatives != null && alternatives.alternatives.isNotEmpty) {
        debugPrint(
            '🛣️ ROUTE: Found ${alternatives.alternatives.length} alternatives, updating with station counts...');

        // Update charging stations count for each alternative
        final updatedAlternatives =
            await _updateAlternativesWithStationCounts(alternatives);

        state = state.copyWith(
          routeAlternatives: updatedAlternatives,
          route: updatedAlternatives.currentRoute.route,
          isLoading: false,
          isLoadingAlternatives: false,
          error: null,
          selectedRouteId: updatedAlternatives.currentRoute.id,
        );

        debugPrint(
            '✅ ROUTE: Found ${updatedAlternatives.alternatives.length} route alternatives');
        debugPrint(
            '✅ ROUTE: Selected route has ${updatedAlternatives.currentRoute.route.polylinePoints.length} polyline points');

        // Fetch destination stations for the selected route
        _fetchDestinationStations(
            updatedAlternatives.currentRoute.route.polylinePoints);
      } else {
        debugPrint(
            '🛣️ ROUTE: No alternatives found, falling back to single route calculation...');
        debugPrint(
            '🛣️ ROUTE: No alternatives found for origin: $origin, destination: $destination. Falling back to single route calculation...');
        // Fallback to single route calculation
        await calculateRoute(origin: origin, destination: destination);
        // Ensure state reflects the fallback if calculateRoute itself fails
        if (state.error != null &&
            state.error!.contains('Failed to calculate route')) {
          state = state.copyWith(
            isLoading: false,
            isLoadingAlternatives: false,
            // Keep the error from calculateRoute, but add context
            error:
                'No alternatives found and fallback route calculation failed for origin: $origin, destination: $destination. Error: ${state.error}',
          );
        } else if (state.route == null && state.error == null) {
          // If calculateRoute completed but somehow didn't set a route or an error (edge case)
          state = state.copyWith(
            isLoading: false,
            isLoadingAlternatives: false,
            error:
                'No alternatives found and fallback route calculation did not yield a route for origin: $origin, destination: $destination.',
          );
        }
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingAlternatives: false,
        error:
            'Failed to calculate route alternatives for origin: $origin, destination: $destination: $e',
      );
      debugPrint(
          '❌ ROUTE: Error calculating alternatives for origin: $origin, destination: $destination: $e');
    }
  }

  /// Update route alternatives with charging station counts
  Future<RouteAlternatives> _updateAlternativesWithStationCounts(
      RouteAlternatives alternatives) async {
    debugPrint('🔋 ROUTE: Updating alternatives with charging station counts');

    final List<RouteAlternative> updatedAlternatives = [];

    for (final alternative in alternatives.alternatives) {
      try {
        // COMPREHENSIVE ALTERNATIVE ROUTE API DEBUGGING
        if (kDebugMode) {
          debugPrint(
              '\n🚀 === ALTERNATIVE ROUTE ${alternative.id} API DEBUG ===');
          debugPrint(
              '🌐 SERVICE: DestinationLocationsService.getStationsAlongRouteSampled');
          debugPrint('📦 ROUTE ID: ${alternative.id}');
          debugPrint(
              '📦 ROUTE OPTIMIZATION: ${alternative.optimization.displayName}');
          debugPrint(
              '📦 POLYLINE POINTS COUNT: ${alternative.route.polylinePoints.length}');
          debugPrint('📦 SAMPLE RATE: 10 (every 10th point for performance)');
          debugPrint(
              '📦 ESTIMATED SAMPLED POINTS: ${(alternative.route.polylinePoints.length / 10).ceil()}');
          debugPrint('📦 ROUTE DISTANCE: ${alternative.route.distance}');
          debugPrint('📦 ROUTE DURATION: ${alternative.route.duration}');
          debugPrint(
              '🚀 === END ALTERNATIVE ROUTE ${alternative.id} API DEBUG ===\n');
        }

        final stations =
            await _destinationLocationsService.getStationsAlongRouteSampled(
          alternative.route.polylinePoints,
          sampleRate:
              10, // Use larger sample rate for alternatives to improve performance
        );

        final updatedAlternative = RouteAlternative(
          id: alternative.id,
          route: alternative.route,
          optimization: alternative.optimization,
          chargingStationsCount: stations.length,
          score: RouteAlternative.calculateScore(
            alternative.route,
            alternative.optimization,
            stations.length,
          ),
          metadata: alternative.metadata,
        );

        updatedAlternatives.add(updatedAlternative);
        debugPrint(
            '🔋 Route ${alternative.id}: ${stations.length} charging stations');
      } catch (e) {
        debugPrint(
            '❌ ROUTE: Error getting stations for alternative ${alternative.id}: $e');
        updatedAlternatives.add(alternative); // Keep original if error
      }
    }

    // Sort by updated scores
    updatedAlternatives.sort((a, b) => b.score.compareTo(a.score));

    return RouteAlternatives(
      alternatives: updatedAlternatives,
      selectedRoute:
          updatedAlternatives.isNotEmpty ? updatedAlternatives.first : null,
      origin: alternatives.origin,
      destination: alternatives.destination,
      createdAt: alternatives.createdAt,
    );
  }

  /// Select a specific route alternative
  void selectRouteAlternative(String routeId) {
    if (state.routeAlternatives == null) {
      debugPrint('❌ ROUTE: No alternatives available to select from');
      return;
    }

    final selectedAlternative = state.routeAlternatives!.getRouteById(routeId);
    if (selectedAlternative == null) {
      debugPrint('❌ ROUTE: Route alternative $routeId not found');
      return;
    }

    debugPrint(
        '🛣️ ROUTE: Selecting alternative $routeId (${selectedAlternative.optimization.displayName})');

    state = state.copyWith(
      selectedRouteId: routeId,
      route: selectedAlternative.route,
    );

    // Fetch destination stations for the newly selected route
    _fetchDestinationStations(selectedAlternative.route.polylinePoints);
  }

  /// Clear route alternatives
  void clearRouteAlternatives() {
    state = state.copyWith(
      routeAlternatives: null,
      isLoadingAlternatives: false,
      selectedRouteId: null,
    );
  }

  /// Force refresh polyline data (for debugging)
  void refreshPolylineData() {
    if (state.hasRoute) {
      debugPrint('🔄 ROUTE: Force refreshing polyline data');
      debugPrint(
          '🔄 ROUTE: Current route points: ${state.polylinePoints.length}');

      // Trigger a state update to force polyline provider refresh
      state = state.copyWith(
        route: state.currentRoute,
      );

      debugPrint('🔄 ROUTE: Polyline data refresh completed');
    } else {
      debugPrint('❌ ROUTE: Cannot refresh polyline data - no route available');
    }
  }

  /// Debug method to validate current polyline state
  void debugPolylineState() {
    debugPrint('🔍 ROUTE: === POLYLINE STATE DEBUG ===');
    debugPrint('🔍 ROUTE: Has route: ${state.hasRoute}');
    debugPrint('🔍 ROUTE: Current route: ${state.currentRoute != null}');
    debugPrint('🔍 ROUTE: Polyline points: ${state.polylinePoints.length}');
    debugPrint(
        '🔍 ROUTE: Route alternatives: ${state.routeAlternatives?.alternatives.length ?? 0}');
    debugPrint('🔍 ROUTE: Selected route ID: ${state.selectedRouteId}');

    if (state.polylinePoints.isNotEmpty) {
      debugPrint('🔍 ROUTE: First point: ${state.polylinePoints.first}');
      debugPrint('🔍 ROUTE: Last point: ${state.polylinePoints.last}');
    }

    debugPrint('🔍 ROUTE: === END POLYLINE STATE DEBUG ===');
  }

  /// Force refresh polylines by triggering provider rebuild
  void forcePolylineRefresh() {
    debugPrint('🔄 ROUTE: === FORCING POLYLINE REFRESH ===');
    debugPrint(
        '🔄 ROUTE: Current state - hasRoute: ${state.hasRoute}, points: ${state.polylinePoints.length}');

    // Trigger a state update to force provider refresh
    if (state.hasRoute) {
      state = state.copyWith(
        // Force a minor update to trigger provider rebuild
        isLoading: false,
      );
      debugPrint('🔄 ROUTE: ✅ Polyline refresh triggered');
    } else {
      debugPrint('🔄 ROUTE: ❌ No route available to refresh');
    }
    debugPrint('🔄 ROUTE: === END FORCING POLYLINE REFRESH ===');
  }
}

/// Provider for route state
final routeProvider = StateNotifierProvider<RouteNotifier, RouteState>((ref) {
  final directionsService = ref.watch(directionsServiceProvider);
  final destinationLocationsService =
      ref.watch(destinationLocationsServiceProvider);
  return RouteNotifier(directionsService, destinationLocationsService);
});

/// Provider for polyline set (computed from route state) - SIMPLIFIED AND RELIABLE
final polylineProvider = Provider<Set<Polyline>>((ref) {
  final routeState = ref.watch(routeProvider);

  debugPrint('🗺️ POLYLINE PROVIDER: === START ===');
  debugPrint('🗺️ POLYLINE PROVIDER: hasRoute: ${routeState.hasRoute}');
  debugPrint(
      '🗺️ POLYLINE PROVIDER: polylinePoints count: ${routeState.polylinePoints.length}');
  debugPrint('🗺️ POLYLINE PROVIDER: route error: ${routeState.error}');
  debugPrint('🗺️ POLYLINE PROVIDER: route loading: ${routeState.isLoading}');

  final Set<Polyline> polylines = {};

  // Simple condition: create polylines if we have route data
  if (routeState.hasRoute && routeState.polylinePoints.isNotEmpty) {
    final polylinePoints = routeState.polylinePoints;

    debugPrint(
        '🗺️ POLYLINE PROVIDER: Creating polyline with ${polylinePoints.length} points');
    debugPrint('🗺️ POLYLINE PROVIDER: First point: ${polylinePoints.first}');
    debugPrint('🗺️ POLYLINE PROVIDER: Last point: ${polylinePoints.last}');

    // Create a single, simple, highly visible polyline
    final mainPolyline = Polyline(
      polylineId: const PolylineId('main_route'),
      points: polylinePoints,
      color: const Color(0xFF2196F3), // Material Blue - highly visible
      width: 8, // Reasonable width
      patterns: [], // Solid line
      startCap: Cap.roundCap,
      endCap: Cap.roundCap,
      jointType: JointType.round,
      zIndex: 100, // High but reasonable z-index
      consumeTapEvents: true,
    );

    polylines.add(mainPolyline);
    debugPrint(
        '🗺️ POLYLINE PROVIDER: ✅ Created main polyline: Blue, width=8, zIndex=100');
  }

  // Handle route alternatives with simpler logic
  if (routeState.hasRouteAlternatives && routeState.routeAlternatives != null) {
    final alternatives = routeState.routeAlternatives!.alternatives;
    debugPrint(
        '🗺️ POLYLINE PROVIDER: Adding ${alternatives.length} route alternatives');

    for (int i = 0; i < alternatives.length; i++) {
      final alternative = alternatives[i];
      final polylinePoints = alternative.route.polylinePoints;

      if (polylinePoints.isEmpty) continue;

      // Skip the selected route to avoid duplication
      if (alternative.id == routeState.selectedRouteId) continue;

      final altPolyline = Polyline(
        polylineId: PolylineId('alt_${alternative.id}'),
        points: polylinePoints,
        color: const Color(0xFF9E9E9E), // Gray for alternatives
        width: 6,
        patterns: [PatternItem.dash(10), PatternItem.gap(5)],
        startCap: Cap.roundCap,
        endCap: Cap.roundCap,
        jointType: JointType.round,
        zIndex: 50 + i,
        consumeTapEvents: true,
      );

      polylines.add(altPolyline);
      debugPrint('🗺️ POLYLINE PROVIDER: Added alternative $i: Gray dashed');
    }
  }

  debugPrint('🗺️ POLYLINE PROVIDER: TOTAL POLYLINES: ${polylines.length}');

  if (polylines.isEmpty && routeState.hasRoute) {
    debugPrint(
        '🗺️ POLYLINE PROVIDER: ❌ WARNING: Route exists but no polylines created!');
    debugPrint(
        '🗺️ POLYLINE PROVIDER: Route points: ${routeState.polylinePoints.length}');
  } else if (polylines.isNotEmpty) {
    debugPrint(
        '🗺️ POLYLINE PROVIDER: ✅ SUCCESS: ${polylines.length} polylines ready for display');
  }

  debugPrint('🗺️ POLYLINE PROVIDER: === END ===');
  return polylines;
});

/// Provider for route bounds (for camera fitting)
final routeBoundsProvider = Provider<LatLngBounds?>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.routeBounds;
});

/// Provider for destination stations along the route
final destinationStationsProvider = Provider<List<DestinationStation>>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.destinationStations;
});

/// Provider for destination stations loading state
final destinationStationsLoadingProvider = Provider<bool>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.isLoadingStations;
});

/// Provider for destination stations as map format (for compatibility with GoogleMapWidget)
final destinationStationsMapProvider =
    Provider<List<Map<String, dynamic>>>((ref) {
  final stations = ref.watch(destinationStationsProvider);

  if (kDebugMode && stations.isNotEmpty) {
    debugPrint(
        '🗺️ MAP STATIONS: === DESTINATION STATIONS MAP PROVIDER DEBUG ===');
    debugPrint(
        '🗺️ MAP STATIONS: Converting ${stations.length} stations to map format');

    for (int i = 0; i < stations.length && i < 3; i++) {
      final station = stations[i];
      final stationMap = station.toStationMap();
      debugPrint('🗺️ MAP STATIONS: Station ${i + 1}:');
      debugPrint('   ID: ${stationMap['id']}');
      debugPrint('   Name: ${stationMap['name']}');
      debugPrint(
          '   Coordinates: (${stationMap['latitude']}, ${stationMap['longitude']})');
      debugPrint('   Status: ${stationMap['status']}');
      debugPrint('   MapPinUrl: ${stationMap['mapPinUrl']}');
      debugPrint('   FocusedMapPinUrl: ${stationMap['focusedMapPinUrl']}');
    }

    if (stations.length > 3) {
      debugPrint(
          '🗺️ MAP STATIONS: ... and ${stations.length - 3} more stations');
    }

    debugPrint('🗺️ MAP STATIONS: === END DESTINATION STATIONS MAP DEBUG ===');
  }

  return stations.map((station) => station.toStationMap()).toList();
});

/// Provider for route alternatives
final routeAlternativesProvider = Provider<RouteAlternatives?>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.routeAlternatives;
});

/// Provider for checking if route alternatives are available
final hasRouteAlternativesProvider = Provider<bool>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.hasRouteAlternatives;
});

/// Provider for the currently selected route alternative
final selectedRouteAlternativeProvider = Provider<RouteAlternative?>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.selectedRouteAlternative;
});

/// Provider for route alternatives loading state
final routeAlternativesLoadingProvider = Provider<bool>((ref) {
  final routeState = ref.watch(routeProvider);
  return routeState.isLoadingAlternatives;
});
