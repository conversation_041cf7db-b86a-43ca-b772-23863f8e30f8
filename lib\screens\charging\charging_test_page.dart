import 'package:flutter/material.dart';
// import 'package:ecoplug/screens/charging_initialization_screen.dart'; //  
// import 'package:ecoplug/screens/charging_session_screen.dart'; // 

/// A test page to directly access charging screens for testing purposes
class ChargingTestPage extends StatelessWidget {
  const ChargingTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Charging Test Page'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Card for Charging Initialization Screen
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      'Charging Initialization Screen',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'This screen shows the initialization process when starting a charging session.',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                         /*
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const ChargingInitializationScreen(),
                          ),
                        );
                        */

                        // Temporary placeholder
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Charging Initialization - Implementation pending'),
                            duration: Duration(seconds: 1),
                          ),
                        );
                      },
                      child: const Text('Open Initialization Screen'),
                    ),
                  ],
                ),
              ),
            ),

            // Card for Charging Session Screen
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      'Charging Session Screen',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'This screen shows the active charging session with real-time metrics.',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                         /*
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ChargingSessionScreen(
                              // Using normal mode with no station or connector IDs
                              initialCharge: 0.3, // Start at 30% charge
                            ),
                          ),
                        );
                        */

                        // Temporary placeholder
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Charging Session - Implementation pending'),
                            duration: Duration(seconds: 1),
                          ),
                        );
                      },
                      child: const Text('Open Session Screen'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        // Show a dialog to input station and connector IDs
                        _showInputDialog(context);
                      },
                      child: const Text('Open Session Screen (Custom)'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Dialog to input station and connector IDs
  void _showInputDialog(BuildContext context) {
    String stationUid = '';
    String connectorId = '';
    double initialCharge = 0.3;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Enter Charging Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Station UID',
                  hintText: 'Enter station UID',
                ),
                onChanged: (value) => stationUid = value,
              ),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Connector ID',
                  hintText: 'Enter connector ID',
                ),
                onChanged: (value) => connectorId = value,
              ),
              const SizedBox(height: 16),
              StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Initial Charge: ${(initialCharge * 100).toInt()}%'),
                      Slider(
                        value: initialCharge,
                        min: 0.0,
                        max: 1.0,
                        divisions: 10,
                        onChanged: (value) {
                          setState(() {
                            initialCharge = value;
                          });
                        },
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
               /*
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChargingSessionScreen(
                    stationUid: stationUid.isNotEmpty ? stationUid : null,
                    connectorId: connectorId.isNotEmpty ? connectorId : null,
                    initialCharge: initialCharge,
                  ),
                ),
              );
              */

              // Temporary placeholder
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Custom Charging Session - Implementation pending'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
            child: const Text('Start'),
          ),
        ],
      ),
    );
  }
}
