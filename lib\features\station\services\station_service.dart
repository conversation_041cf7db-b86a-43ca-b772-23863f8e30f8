import 'package:flutter/foundation.dart' show debugPrint;
import 'package:ecoplug/services/connectivity_service.dart';
import 'package:ecoplug/models/station/station_detail_models.dart'
    as detail_models;
import 'package:ecoplug/models/station/station_models.dart';
import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/core/models/api_response.dart';
import 'package:ecoplug/models/station/station_detail.dart' as station_detail;
import 'package:ecoplug/core/api/api_service.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/models/station/paginated_stations_response.dart';

/// Service for handling station-related operations
class StationService {
  final ConnectivityService _connectivityService;
  final StationRepository _stationRepository;
  final ApiService _apiService;

  StationService(
      this._connectivityService, this._stationRepository, this._apiService);

  // Cache for station data
  List<StationMarker>? _cachedMarkers;
  DateTime? _markersLastFetched;

  /// Get station markers for map display
  /// Uses caching to reduce API calls
  Future<ApiResponse<List<StationMarker>>> getStationMarkers(
      {bool forceRefresh = false}) async {
    // Check if we have cached data and it's not too old (less than 5 minutes)
    final now = DateTime.now();
    final cacheValid = _cachedMarkers != null &&
        _markersLastFetched != null &&
        now.difference(_markersLastFetched!).inMinutes < 5;

    // Return cached data if valid and not forcing refresh
    if (cacheValid && !forceRefresh) {
      return ApiResponse<List<StationMarker>>(
        success: true,
        message: 'Station markers retrieved from cache',
        data: _cachedMarkers!,
      );
    }

    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      // Return cached data if available, even if old
      if (_cachedMarkers != null) {
        return ApiResponse<List<StationMarker>>(
          success: true,
          message: 'Using cached station markers (offline)',
          data: _cachedMarkers!,
        );
      } else {
        return ApiResponse<List<StationMarker>>(
          success: false,
          message: 'No internet connection and no cached data available',
        );
      }
    }

    // Make API call
    final response = await _stationRepository.getStationMarkers();

    // Update cache if successful
    if (response.success && response.data != null) {
      _cachedMarkers = response.data
          ?.map((marker) => StationMarker.fromJson(marker.toJson()))
          .toList();
      _markersLastFetched = now;
    }

    return ApiResponse<List<StationMarker>>(
      success: response.success,
      message: response.message,
      data: _cachedMarkers,
    );
  }

  /// Get nearest stations
  Future<ApiResponse<List<detail_models.StationDetail>>> getNearestStations(
      double latitude, double longitude,
      {double radius = 5.0}) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<detail_models.StationDetail>>(
        success: false,
        message: 'No internet connection',
      );
    }

    final response = await _stationRepository
        .getNearestStations(latitude, longitude, radius: radius);
    if (response.success && response.data != null) {
      return ApiResponse<List<detail_models.StationDetail>>(
        success: true,
        message: response.message,
        data: response.data!
            .map((station) =>
                detail_models.StationDetail.fromJson(station.toJson()))
            .toList(),
      );
    }
    return ApiResponse<List<detail_models.StationDetail>>(
      success: false,
      message: response.message,
    );
  }

  /// Get station details by ID
  /// No caching is used; always fetches fresh data from API
  Future<ApiResponse<detail_models.StationDetail>> getStationDetails(
      String stationId) async {
    // Check connectivity before API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<detail_models.StationDetail>(
        success: false,
        message: 'No internet connection',
      );
    }

    final response = await _stationRepository.getStationDetailsByUid(stationId);
    if (response.success && response.data != null) {
      return ApiResponse<detail_models.StationDetail>(
        success: true,
        message: response.message,
        data: detail_models.StationDetail.fromJson(response.data!.toJson()),
      );
    }
    return ApiResponse<detail_models.StationDetail>(
      success: false,
      message: response.message,
    );
  }

  /// Search stations by query
  Future<ApiResponse<List<detail_models.StationDetail>>> searchStations(
      String query) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<detail_models.StationDetail>>(
        success: false,
        message: 'No internet connection',
      );
    }

    final response = await _stationRepository.searchStations(query);
    if (response.success && response.data != null) {
      return ApiResponse<List<detail_models.StationDetail>>(
        success: true,
        message: response.message,
        data: response.data!
            .map((station) =>
                detail_models.StationDetail.fromJson(station.toJson()))
            .toList(),
      );
    }
    return ApiResponse<List<detail_models.StationDetail>>(
      success: false,
      message: response.message,
    );
  }

  /// Get station markers
  Future<ApiResponse<List<StationMarker>>> getStationMarkersInArea(
      {required double latitude,
      required double longitude,
      double radius = 5.0}) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<StationMarker>>(
        success: false,
        message: 'No internet connection',
      );
    }

    final response = await _stationRepository.getStationMarkers();
    return ApiResponse<List<StationMarker>>(
      success: response.success,
      message: response.message,
      data: response.data
          ?.map((marker) => StationMarker.fromJson(marker.toJson()))
          .toList(),
    );
  }

  /// Save station review
  Future<ApiResponse<void>> saveStationReview(
      String stationId, String review, double rating) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<void>(
        success: false,
        message: 'No internet connection',
      );
    }

    final response = await _stationRepository.saveReview(
      stationId,
      rating,
      review,
    );

    return ApiResponse<void>(
      success: response.success,
      message: response.message,
    );
  }

  /// Save station bookmark
  Future<ApiResponse<void>> saveStationBookmark(
      String stationId, bool isBookmarked) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<void>(
        success: false,
        message: 'No internet connection',
      );
    }

    if (isBookmarked) {
      final response = await _stationRepository.saveBookmark(stationId);
      return ApiResponse<void>(
        success: response.success,
        message: response.message,
      );
    } else {
      // TODO: Implement remove bookmark when available in repository
      return ApiResponse<void>(
        success: false,
        message: 'Removing bookmarks is not yet supported',
      );
    }
  }

  /// Get bookmarked stations
  Future<ApiResponse<List<detail_models.StationDetail>>>
      getBookmarkedStations() async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<detail_models.StationDetail>>(
        success: false,
        message: 'No internet connection',
      );
    }

    final response = await _stationRepository.getBookmarkedStations();
    if (response.success && response.data != null) {
      return ApiResponse<List<detail_models.StationDetail>>(
        success: true,
        message: response.message,
        data: response.data!
            .map((station) =>
                detail_models.StationDetail.fromJson(station.toJson()))
            .toList(),
      );
    }
    return ApiResponse<List<detail_models.StationDetail>>(
      success: false,
      message: response.message,
    );
  }

  /// Clear station cache
  void clearCache() {
    _cachedMarkers = null;
    _markersLastFetched = null;
    debugPrint('Station cache cleared');
  }

  /// Fetch detailed information for a station by its UID using REAL API
  Future<station_detail.StationDetail> fetchStationDetails(
      String stationUid) async {
    // Check connectivity if available
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      throw Exception('No internet connection');
    }

    debugPrint(
        '🔌 StationService: Fetching REAL station details for UID: $stationUid');

    try {
      // REAL API CALL - No more mock data!
      // Use the same endpoint as the working station details page
      final response =
          await _apiService.get('user/stations/details?uid=$stationUid');

      debugPrint(
          '✅ StationService: Received real API response for station details');

      // Validate response structure
      if (response == null) {
        throw Exception('Empty response from station details API');
      }

      if (response is! Map<String, dynamic>) {
        throw Exception('Invalid response format from station details API');
      }

      // Check for success field
      if (response['success'] != true) {
        final message = response['message'] ?? 'Unknown error from API';
        throw Exception('API error: $message');
      }

      // Extract data field
      final data = response['data'];
      if (data == null) {
        throw Exception('No data field in API response');
      }

      debugPrint('🎯 StationService: Processing real station data from API');
      debugPrint('Station name: ${data['name']}');
      debugPrint('Station address: ${data['address']}');
      debugPrint('Station rating: ${data['rate']}');
      debugPrint('Station reviews: ${data['rate_total']}');

      // Transform API response to match StationDetail.fromJson expected format
      final transformedResponse = {
        "uid": data['uid'] ??
            stationUid, // Use request UID if API doesn't provide one
        "name": data['name'] ?? 'Station Name Not Available',
        "address": data['address'] ?? 'Address Not Available',
        "location_details": {
          "city": data['city'],
          "state": data['state'],
          "postal_code": null, // Not provided in API
          "country": null, // Not provided in API
        },
        "coordinates": {
          "latitude": data['latitude'] ?? 0.0,
          "longitude": data['longitude'] ?? 0.0
        },
        "status": data['open_status'] == true ? 'Available' : 'Unavailable',
        "connectors": _extractConnectorsFromEvses(data['evses']),
        "amenities": [], // Not provided in current API structure
        "operating_hours": data['opening_times'] ?? 'Hours not specified',
        "images": data['images'] != null && data['images'].isNotEmpty
            ? [data['images']]
            : [
                'https://api2.eeil.online/uploads/ev-banner2.png'
              ], // Real API default image
        "rating": (data['rate'] as num?)?.toDouble() ?? 0.0,
        "total_reviews": (data['rate_total'] as num?)?.toInt() ?? 0,
        "operator_name": "EcoPlug Network" // Default operator name
      };

      debugPrint('✅ StationService: Successfully transformed real API data');
      return station_detail.StationDetail.fromJson(transformedResponse);
    } catch (e) {
      debugPrint('❌ StationService: Error fetching real station details: $e');
      throw Exception('Failed to fetch station details: $e');
    }
  }

  /// Extract connectors from EVSEs data structure
  List<Map<String, dynamic>> _extractConnectorsFromEvses(
      Map<String, dynamic>? evses) {
    final List<Map<String, dynamic>> connectors = [];

    if (evses == null || evses.isEmpty) {
      debugPrint('⚠️ No EVSEs data available');
      return connectors;
    }

    evses.forEach((evsesUid, evse) {
      if (evse['connectors'] != null) {
        for (var connector in evse['connectors']) {
          connectors.add({
            "id": connector['connector_id'] ?? 'Unknown',
            "type": connector['type'] ?? 'Unknown Type',
            "status": connector['status'] ?? 'Unknown',
            "power_kw":
                (connector['max_electric_power'] as num?)?.toDouble() ?? 0.0,
            "price_text": connector['price_label'] ?? 'Price not available'
          });
        }
      }
    });

    debugPrint('🔌 Extracted ${connectors.length} real connectors from EVSEs');
    return connectors;
  }

  Future<PaginatedStationsResponse> fetchStationsPaginated({
    int page = 1,
    int limit = 10,
    double? latitude,
    double? longitude,
    String? powerOutput,
    List<String>? connectorStandards,
    Map<String, dynamic>? filters,
  }) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      throw Exception('No internet connection');
    }

    try {
      // Validate parameters
      if (page < 1) page = 1;
      if (limit < 1) limit = 10;

      // Log the API call for debugging
      debugPrint(
          'StationService: Fetching paginated stations - page: $page, limit: $limit');
      if (latitude != null && longitude != null) {
        debugPrint(
            'StationService: Including user location: $latitude, $longitude');
      }
      if (powerOutput != null) {
        debugPrint(
            'StationService: Including power output filter: $powerOutput');
      }
      if (connectorStandards != null && connectorStandards.isNotEmpty) {
        debugPrint(
            'StationService: Including connector standards filter: $connectorStandards');
      }

      // Use the enhanced API service method with dynamic URL construction
      final response = await _apiService.fetchPaginatedStations(
        page: page,
        limit: limit,
        latitude: latitude,
        longitude: longitude,
        powerOutput: powerOutput,
        connectorStandards: connectorStandards,
      );

      // Convert PaginatedStationResponse to PaginatedStationsResponse
      // Since PaginatedStationResponse doesn't have pagination fields, we need to convert the data
      final List<PaginatedStation> paginatedStations =
          response.data.map((station) {
        return PaginatedStation(
          stationId: int.tryParse(station.id),
          uid: station.uid,
          name: station.name,
          address: station.address,
          city: station.city,
          latitude: station.latitude,
          longitude: station.longitude,
          distance: station.distance,
          status: station.status,
          rating: station.rating,
          reviewCount: station.reviews,
          imageUrl: station.images.isNotEmpty ? station.images.first : null,
          types: station.types,
        );
      }).toList();

      final result = PaginatedStationsResponse(
        success: response.success,
        message: response.message,
        currentPage:
            page, // Use the requested page since API response doesn't include it
        totalPages: 1, // Default to 1 since API response doesn't include it
        data: paginatedStations,
      );

      // Log parsed result
      debugPrint(
          'StationService: Parsed response - currentPage: ${result.currentPage}, totalPages: ${result.totalPages}, stations count: ${result.data?.length ?? 0}');

      return result;
    } catch (e) {
      debugPrint('StationService: Error fetching paginated stations: $e');
      throw Exception('Failed to load stations: $e');
    }
  }

  Future<List<Station>> searchStationsAlternative(String query,
      {int page = 1, int limit = 20}) async {
    try {
      final response = await _apiService.get(
          'station/search?name=${Uri.encodeComponent(query)}&page=$page&limit=$limit');

      if (response is Map<String, dynamic> &&
          response.containsKey('data') &&
          response['data'] is List) {
        final List<dynamic> data = response['data'];
        return data
            .map((stationJson) =>
                Station.fromJson(stationJson as Map<String, dynamic>))
            .toList();
      } else if (response is List) {
        return response
            .map((stationJson) =>
                Station.fromJson(stationJson as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Unexpected format for search stations response');
      }
    } catch (e) {
      throw Exception('Failed to search stations: $e');
    }
  }

  Future<Station> getStationDetailsAsStation(String stationUid) async {
    try {
      final response = await _apiService.get('station/$stationUid');
      if (response is Map<String, dynamic> && response.containsKey('data')) {
        return Station.fromJson(response['data'] as Map<String, dynamic>);
      }
      return Station.fromJson(response as Map<String, dynamic>);
    } catch (e) {
      throw Exception('Failed to load station details: $e');
    }
  }
}
