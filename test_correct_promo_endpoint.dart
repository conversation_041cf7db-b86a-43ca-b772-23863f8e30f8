import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Test the correct promo code verification endpoint
class CorrectPromoEndpointTester {
  static const String baseUrl = 'https://api2.eeil.online/api/v1';
  static const String correctEndpoint = '$baseUrl/user/promocodes/verify';
  
  /// Test the correct endpoint format
  static Future<void> testCorrectEndpoint() async {
    print('🔔 ===== TESTING CORRECT PROMO CODE ENDPOINT =====');
    print('🔔 Endpoint: $correctEndpoint');
    print('🔔 Method: POST');
    print('🔔 ================================================\n');

    final testCodes = ['power500', 'SAVE15', 'WELCOME10', 'TEST123'];
    
    for (final code in testCodes) {
      await testPromoCodeWithDifferentFormats(code);
    }
    
    print('\n🔔 ===== TESTING COMPLETED =====');
  }
  
  /// Test different request formats for the promo code
  static Future<void> testPromoCodeWithDifferentFormats(String promoCode) async {
    print('🎫 Testing promo code: $promoCode');
    print('-' * 40);
    
    // Test Format 1: Query parameter (as you mentioned)
    await testWithQueryParameter(promoCode);
    
    // Test Format 2: JSON body with 'promo' field
    await testWithJsonBody(promoCode, {'promo': promoCode});
    
    // Test Format 3: JSON body with 'code' field
    await testWithJsonBody(promoCode, {'code': promoCode});
    
    // Test Format 4: JSON body with 'promo_code' field
    await testWithJsonBody(promoCode, {'promo_code': promoCode});
    
    print('');
  }
  
  /// Test with query parameter
  static Future<void> testWithQueryParameter(String promoCode) async {
    print('📤 Format 1: Query Parameter (?promo=$promoCode)');
    
    try {
      final url = '$correctEndpoint?promo=$promoCode';
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(Duration(seconds: 10));
      
      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body}');
      
      if (response.statusCode == 200) {
        print('✅ SUCCESS with query parameter!');
      } else if (response.statusCode == 401) {
        print('🔐 NEEDS AUTH but endpoint format is correct!');
      }
      
    } catch (e) {
      print('❌ Error: $e');
    }
    print('');
  }
  
  /// Test with JSON body
  static Future<void> testWithJsonBody(String promoCode, Map<String, dynamic> payload) async {
    print('📤 Format: JSON Body ${jsonEncode(payload)}');
    
    try {
      final response = await http.post(
        Uri.parse(correctEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(payload),
      ).timeout(Duration(seconds: 10));
      
      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body.length > 150 ? response.body.substring(0, 150) + "..." : response.body}');
      
      if (response.statusCode == 200) {
        print('✅ SUCCESS with JSON body!');
      } else if (response.statusCode == 401) {
        print('🔐 NEEDS AUTH but endpoint format is correct!');
      }
      
    } catch (e) {
      print('❌ Error: $e');
    }
    print('');
  }
  
  /// Test with authentication
  static Future<void> testWithAuth() async {
    print('🔐 Testing with dummy authentication...');
    
    try {
      final response = await http.post(
        Uri.parse('$correctEndpoint?promo=power500'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer dummy_token_for_testing',
        },
      ).timeout(Duration(seconds: 10));
      
      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body}');
      
    } catch (e) {
      print('❌ Error: $e');
    }
  }
}

/// Main function to run the tests
void main() async {
  await CorrectPromoEndpointTester.testCorrectEndpoint();
  await CorrectPromoEndpointTester.testWithAuth();
}
