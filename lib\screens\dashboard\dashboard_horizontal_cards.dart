import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:ecoplug/utils/app_themes.dart';
import 'package:ecoplug/screens/station/station_list_page.dart';
import 'package:ecoplug/screens/station/station_details_page.dart';
import 'package:ecoplug/screens/dashboard/filter_dialog.dart';
import 'package:ecoplug/screens/dashboard/google_map_widget.dart';
import 'package:ecoplug/services/location_service.dart';
import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/services/connectivity_monitor.dart';
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/utils/map_marker_utils.dart';
import 'package:ecoplug/services/ongoing_sessions_service.dart';

class DashboardHorizontalCards extends ConsumerStatefulWidget {
  const DashboardHorizontalCards({super.key});

  @override
  ConsumerState<DashboardHorizontalCards> createState() =>
      _DashboardHorizontalCardsState();
}

class _DashboardHorizontalCardsState
    extends ConsumerState<DashboardHorizontalCards>
    with TickerProviderStateMixin {
  // Core services
  final LocationService _locationService = LocationService();
  final StationRepository _stationRepository = StationRepository();
  final ConnectivityMonitor _connectivityMonitor = ConnectivityMonitor();
  final ApiBridge _apiBridge = ApiBridge();
  final OngoingSessionsService _ongoingSessionsService =
      OngoingSessionsService();

  // Map and location state
  final GlobalKey<GoogleMapWidgetState> _googleMapKey =
      GlobalKey<GoogleMapWidgetState>();
  double? _currentLatitude;
  double? _currentLongitude;
  LatLng? _userActivityLocation;
  bool _hasLocationUpdate = false;

  // Track last focused station to ensure map updates
  LatLng? _lastFocusedLocation;
  DateTime? _lastMapFocusTime;

  // Debouncing for map updates during rapid swiping
  Timer? _mapUpdateTimer;
  Timer? _mapRetryTimer; // Track retry timers to prevent accumulation
  int _lastProcessedPageIndex = -1;
  int _mapRetryCount = 0; // Track retry attempts to prevent infinite loops
  static const int _maxMapRetries = 3; // Maximum retry attempts

  // Swipe threshold variables
  double _swipeStartPosition = 0.0;
  bool _isSwipeInProgress = false;
  static const double _swipeThreshold =
      75.0; // Minimum swipe distance in pixels

  // Station data
  List<Map<String, dynamic>> _stations = [];
  List<Map<String, dynamic>> _filteredStations = [];
  List<Map<String, dynamic>> _allMapStations =
      []; // All marker API stations for map display
  bool _isLoadingStations = false;
  bool _isLoadingNearestStations = false;
  bool _isLoadingMapMarkers = false;
  String? _errorMessage;
  bool _showingNearestStations = true;

  // Search and filter state
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isSearchLoading = false;
  bool _showFilterOptions = false;
  Map<String, bool> _selectedConnectorFilters = {};
  String _selectedPowerOutput = 'All';
  final double _searchRadius =
      50.0; // Default 50km radius - matching dashboard_screen.dart

  // UI state
  PageController _pageController = PageController();
  bool _isSearchExpanded = false;

  // Animation controllers
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  // Battery pulsing animation controllers (separate from refresh) - copied from dashboard_screen.dart
  late AnimationController _batteryPulseAnimationController;
  late Animation<double> _batteryPulseAnimation;

  // Active sessions state
  bool _hasActiveSessions = false;
  bool _isCheckingActiveSessions = false;
  Timer? _ongoingSessionsTimer;

  // Constants
  static const double _indiaLatitude = 20.5937;
  static const double _indiaLongitude = 78.9629;

  @override
  void initState() {
    super.initState();
    debugPrint(
        '🚀 DashboardHorizontalCards: initState() called - starting initialization...');

    _initializeConnectorFilters();
    _initializeAnimations();
    _initializeLocationAndLoadStations();
    _loadAllMapMarkers(); // Load all marker API stations for map display
    _checkOngoingSessions(); // Check for active charging sessions
    _startOngoingSessionsTimer(); // Start periodic session checking

    debugPrint('🚀 DashboardHorizontalCards: initState() completed');
  }

  void _initializeAnimations() {
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.easeInOut,
    ));

    // Initialize battery pulse animation controller (separate from refresh) - copied from dashboard_screen.dart
    _batteryPulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000), // 1 second pulse
    );

    _batteryPulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _batteryPulseAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeConnectorFilters() {
    _selectedConnectorFilters.clear();
    for (String connectorType in FilterDialog.availableConnectorTypes) {
      _selectedConnectorFilters[connectorType] = false;
    }
  }

  Future<void> _initializeLocationAndLoadStations() async {
    debugPrint(
        '🌍 DashboardHorizontalCards: Starting location initialization...');

    setState(() {
      _isLoadingStations = true;
      _isLoadingNearestStations = true;
    });

    try {
      // First, try to get user location with improved timeout and error handling
      debugPrint('📍 Attempting to get user location...');
      final position = await _locationService.getCurrentLocation();

      if (position != null && mounted) {
        setState(() {
          _currentLatitude = position.latitude;
          _currentLongitude = position.longitude;
          _userActivityLocation = LatLng(position.latitude, position.longitude);
          _hasLocationUpdate = true;
        });

        debugPrint(
            '✅ Location obtained: ${position.latitude}, ${position.longitude}');

        // Focus map on user location immediately after obtaining it
        _focusMapOnUserLocation();

        // Load nearest stations with user's actual location
        await _loadNearestStations();
      } else {
        debugPrint(
            '⚠️ Location not available, attempting to get location with extended timeout...');

        // Try one more time with extended timeout for first-time users
        final retryPosition = await _getLocationWithExtendedTimeout();

        if (retryPosition != null && mounted) {
          setState(() {
            _currentLatitude = retryPosition.latitude;
            _currentLongitude = retryPosition.longitude;
            _userActivityLocation =
                LatLng(retryPosition.latitude, retryPosition.longitude);
            _hasLocationUpdate = true;
          });

          debugPrint(
              '✅ Location obtained on retry: ${retryPosition.latitude}, ${retryPosition.longitude}');

          // Focus map on user location
          _focusMapOnUserLocation();

          // Load nearest stations with user's actual location
          await _loadNearestStations();
        } else {
          debugPrint(
              '❌ Location detection failed completely - user needs to manually enable location');
          setState(() {
            _errorMessage =
                'Location access required to find nearby stations. Please enable location services and tap the location button.';
            _isLoadingStations = false;
            _isLoadingNearestStations = false;
          });
        }
      }
    } catch (e) {
      debugPrint('❌ Error getting location: $e');
      setState(() {
        _errorMessage =
            'Unable to access location. Please enable location services and tap the location button to find nearby stations.';
        _isLoadingStations = false;
        _isLoadingNearestStations = false;
      });
    }
  }

  /// Get location with extended timeout for first-time app launches
  Future<Position?> _getLocationWithExtendedTimeout() async {
    try {
      debugPrint('📍 Attempting location with extended timeout...');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ Location services are disabled');
        return null;
      }

      // Check for location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        debugPrint('📍 Requesting location permission...');
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('❌ Location permissions denied by user');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('❌ Location permissions permanently denied');
        return null;
      }

      // Get position with extended timeout for first-time users
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 15), // Extended timeout for first launch
        ),
      );

      debugPrint('✅ Extended timeout location successful');
      return position;
    } catch (e) {
      debugPrint('❌ Extended timeout location failed: $e');
      return null;
    }
  }

  /// Check for ongoing charging sessions
  Future<void> _checkOngoingSessions() async {
    if (!mounted) return;

    setState(() {
      _isCheckingActiveSessions = true;
    });

    try {
      debugPrint(
          '🔋 DashboardHorizontalCards: Checking for ongoing sessions...');

      final sessions = await _ongoingSessionsService.getOngoingSessions();

      if (mounted) {
        setState(() {
          _hasActiveSessions = sessions?.hasActiveSessions ?? false;
          _isCheckingActiveSessions = false;
        });

        if (_hasActiveSessions && sessions != null) {
          debugPrint(
              '🔋 DashboardHorizontalCards: Found ${sessions.activeSessionsCount} active session(s)');
          // Start pulsing animation if there are active sessions
          _startBatteryPulsingAnimation();
        } else {
          debugPrint('🔋 DashboardHorizontalCards: No active sessions found');
        }
      }
    } catch (e) {
      debugPrint(
          '❌ DashboardHorizontalCards: Error checking ongoing sessions: $e');
      if (mounted) {
        setState(() {
          _hasActiveSessions = false;
          _isCheckingActiveSessions = false;
        });
      }
    }
  }

  /// Start timer for periodic ongoing sessions checking
  void _startOngoingSessionsTimer() {
    _ongoingSessionsTimer?.cancel();
    _ongoingSessionsTimer = Timer.periodic(
      const Duration(seconds: 30), // Check every 30 seconds
      (timer) {
        if (mounted) {
          _checkOngoingSessions();
        } else {
          timer.cancel();
        }
      },
    );
  }

  /// Start pulsing animation for battery icon - copied from dashboard_screen.dart
  void _startBatteryPulsingAnimation() {
    if (!_batteryPulseAnimationController.isAnimating) {
      _batteryPulseAnimationController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    debugPrint('🗺️ DashboardHorizontalCards: Starting disposal...');

    // Cancel all timers to prevent memory leaks
    _mapUpdateTimer?.cancel();
    _mapRetryTimer?.cancel();
    _ongoingSessionsTimer?.cancel();

    // Dispose animation controllers
    _searchAnimationController.dispose();
    _batteryPulseAnimationController.dispose();

    // Dispose other controllers
    _pageController.dispose();
    _searchController.dispose();

    // Reset state to prevent any lingering references
    _mapRetryCount = 0;
    _lastProcessedPageIndex = -1;
    _lastFocusedLocation = null;
    _lastMapFocusTime = null;

    debugPrint('🗺️ DashboardHorizontalCards: Disposal completed');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Google Map as background
          _buildGoogleMap(),

          // Top search and filter bar
          _buildTopSearchBar(),

          // Active Sessions Battery Icon - Top Left Position
          if (_hasActiveSessions)
            Positioned(
              left: 16,
              top: MediaQuery.of(context).padding.top + 80, // Below search bar
              child: _buildActiveSessionsBatteryIcon(),
            ),

          // Horizontal station cards at bottom
          _buildHorizontalStationCards(),

          // Floating action buttons
          _buildFloatingActionButtons(),
        ],
      ),
    );
  }

  Widget _buildGoogleMap() {
    // Use higher zoom when user location is available, lower zoom for default location
    final double initialZoom =
        (_currentLatitude != null && _currentLongitude != null) ? 14.0 : 5.0;

    return GoogleMapWidget(
      key: _googleMapKey,
      initialLatitude: _currentLatitude ?? _indiaLatitude,
      initialLongitude: _currentLongitude ?? _indiaLongitude,
      initialZoom: initialZoom,
      stations: _allMapStations, // Show all marker API stations on map
      onStationSelected: _onStationSelected,
      onCameraPositionChanged: _onCameraPositionChanged,
      enableClustering: true,
    );
  }

  Widget _buildTopSearchBar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      right: 16,
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Search icon/button
            _buildSearchButton(),

            // Filter button
            _buildFilterButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchButton() {
    return Expanded(
      child: GestureDetector(
        onTap: _onSearchTapped,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Icon(
                Icons.search,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Search stations...',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButton() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: _hasActiveFilters
            ? AppThemes.primaryColor.withAlpha(25)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(28),
      ),
      child: IconButton(
        onPressed: _showFilterDialog,
        icon: Icon(
          Icons.tune,
          color: _hasActiveFilters
              ? AppThemes.primaryColor
              : Theme.of(context).colorScheme.onSurface.withAlpha(153),
          size: 24,
        ),
      ),
    );
  }

  Widget _buildHorizontalStationCards() {
    if (_isLoadingStations || _isLoadingNearestStations) {
      return _buildLoadingIndicator();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_filteredStations.isEmpty) {
      return _buildEmptyState();
    }

    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 210, // Optimized height with buttons at bottom edge
        child: GestureDetector(
          onHorizontalDragStart: (details) {
            _swipeStartPosition = details.globalPosition.dx;
            _isSwipeInProgress = true;
          },
          onHorizontalDragUpdate: (details) {
            if (!_isSwipeInProgress) return;

            final currentPosition = details.globalPosition.dx;
            final swipeDistance = (currentPosition - _swipeStartPosition).abs();

            // Only allow PageView to handle the gesture if swipe distance exceeds threshold
            if (swipeDistance < _swipeThreshold) {
              // Prevent PageView from responding to small movements
              return;
            }
          },
          onHorizontalDragEnd: (details) {
            _isSwipeInProgress = false;
          },
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: _onStationPageChanged,
            itemCount: _filteredStations.length,
            physics:
                const BouncingScrollPhysics(), // Smoother scrolling for rapid swipes
            pageSnapping: true, // Ensure pages snap properly
            itemBuilder: (context, index) {
              return _buildStationCard(_filteredStations[index], index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildStationCard(Map<String, dynamic> station, int index) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final stationName = station['name'] ?? 'Unknown Station';
    final address = station['address'] ?? 'No Address';
    final distance = station['distance'] ?? 0.0;
    final availability =
        station['status'] ?? station['availability'] ?? 'Available';

    // Determine color based on availability status
    Color availabilityColor;
    IconData statusIcon;
    if (availability == 'Available') {
      availabilityColor = AppThemes.primaryColor;
      statusIcon = Icons.check_circle;
    } else if (availability == 'In Use') {
      availabilityColor = Colors.orange;
      statusIcon = Icons.flash_on;
    } else {
      availabilityColor = Colors.red;
      statusIcon = Icons.cancel;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(28), // More curved edges
          side: isDarkMode
              ? BorderSide(color: AppThemes.darkBorder, width: 1)
              : BorderSide.none,
        ),
        color: Theme.of(context).colorScheme.surface,
        elevation: isDarkMode ? 0 : 8,
        shadowColor: Colors.black.withAlpha(30),
        child: InkWell(
          onTap: () =>
              _navigateToStationDetails(station), // Navigate to station details
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.fromLTRB(
                16, 16, 16, 8), // Small bottom padding for buttons at edge
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(28),
              gradient: isDarkMode
                  ? null
                  : LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).colorScheme.surface,
                        Theme.of(context).colorScheme.surface.withAlpha(250),
                      ],
                    ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize
                  .max, // Expand to fill space for bottom-aligned buttons
              children: [
                // Header with station icon, name and status
                Row(
                  children: [
                    // Station icon
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppThemes.primaryColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.ev_station,
                        color: AppThemes.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Station name and status
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            stationName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                statusIcon,
                                color: availabilityColor,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                availability,
                                style: TextStyle(
                                  color: availabilityColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Distance badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.location_on,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withAlpha(153),
                            size: 12,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '${distance.toStringAsFixed(1)} km',
                            style: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withAlpha(153),
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4), // Reduced for compact layout

                // Address with location icon
                Row(
                  children: [
                    Icon(
                      Icons.place_outlined,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withAlpha(153),
                      size: 15,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        address,
                        style: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withAlpha(153),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                const SizedBox(
                    height:
                        12), // More spacing to drag connector section towards bottom

                // Connector types section with API icons
                _buildConnectorTypesSection(station),

                // Spacer to push buttons to bottom edge
                const Spacer(),

                // Action buttons row with enhanced design
                Container(
                  width: double.infinity,
                  padding:
                      EdgeInsets.zero, // Zero padding for ultra-compact buttons
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface.withAlpha(8),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    children: [
                      // Start charging button - moved to left side
                      Expanded(
                        flex: 2,
                        child: ElevatedButton.icon(
                          onPressed: () => _navigateToStationDetails(station),
                          icon: const Icon(
                            Icons.power,
                            size: 18,
                            color: Colors.white,
                          ),
                          label: const Text(
                            'Start Charging',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 13,
                              color: Colors.white,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppThemes.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 6), // Reduced for compact layout
                            elevation: 3,
                            shadowColor: AppThemes.primaryColor.withAlpha(100),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Directions button - moved to right side
                      Expanded(
                        flex: 1,
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Colors.blue, // Same as station list
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  16), // Same curved radius as Start Charging button
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 6), // Reduced for compact layout
                            minimumSize: const Size.fromHeight(
                                38), // Reduced height for compact layout
                            elevation:
                                3, // Same elevation as Start Charging button
                          ),
                          onPressed: () => _openDirectionsToStation(
                              station), // Open directions only
                          icon: const Icon(
                            Icons.directions,
                            size: 18,
                            color: Colors.white,
                          ),
                          label: const Text(
                            'Directions',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Focus map on user's current location
  void _focusMapOnUserLocation() {
    if (_currentLatitude != null && _currentLongitude != null) {
      debugPrint(
          '🗺️ Focusing map on user location: $_currentLatitude, $_currentLongitude');

      // Add a small delay to ensure map is fully initialized
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && _googleMapKey.currentState != null) {
          _googleMapKey.currentState!.focusOnLocation(
            _currentLatitude!,
            _currentLongitude!,
            14.0, // Good zoom level for user location
          );
        }
      });
    }
  }

  Future<void> _loadNearestStations() async {
    if (!mounted) return;

    debugPrint('🔄 Loading nearest stations...');
    debugPrint('📍 Current location: $_currentLatitude, $_currentLongitude');
    debugPrint('📍 User activity location: $_userActivityLocation');

    try {
      setState(() {
        _isLoadingNearestStations = true;
        _isSearching = false;
      });

      final LatLng locationToFetch = _userActivityLocation ??
          LatLng(_currentLatitude!, _currentLongitude!);

      debugPrint(
          '📍 Location to fetch stations from: ${locationToFetch.latitude}, ${locationToFetch.longitude}');

      final apiResponse = await _connectivityMonitor.executeApiCall(
        () => _stationRepository.getNearestStations(
          locationToFetch.latitude,
          locationToFetch.longitude,
          radius: _searchRadius,
        ),
        context: mounted ? context : null,
        errorMessage:
            'Unable to load nearby stations. Please check your connection.',
        showErrorOnFailure: false,
      );

      if (apiResponse.success == true && apiResponse.data != null && mounted) {
        debugPrint('API returned ${apiResponse.data!.length} stations');

        final formattedStations = apiResponse.data!.map((station) {
          String status = station.status ?? 'Available';
          final connectorTypeStr = station.getConnectorTypesString();
          final connectorTypes = station.getConnectorTypes();

          // Convert ConnectorType objects to Maps for UI compatibility
          final connectorTypesMap = connectorTypes
              .map((connectorType) => {
                    'name': connectorType.name,
                    'icon': connectorType.icon,
                    'power': connectorType.power,
                    'power_type': connectorType.power,
                    'guns': connectorType.guns,
                    'available_guns': connectorType.availableGuns,
                  })
              .toList();

          double distance = station.distance ?? 0.0;
          final String extractedUid = station.uid;

          return {
            'id': station.stationId.toString(),
            'name': station.name,
            'latitude': station.latitude,
            'longitude': station.longitude,
            'address': station.address,
            'city': station.city,
            'uid': extractedUid,
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'connectorTypes': connectorTypesMap,
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        setState(() {
          _stations = formattedStations;
          _filteredStations = List.from(formattedStations);
          _showingNearestStations = true;
          _errorMessage = null;
        });

        debugPrint('✅ Successfully loaded ${_stations.length} stations');
      } else {
        setState(() {
          _errorMessage = 'No stations found in this area';
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading nearest stations: $e');
      setState(() {
        _errorMessage = 'Failed to load stations';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingStations = false;
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  void _onMarkerTapped(String markerId) {
    debugPrint('🎯 Marker tapped: $markerId');

    // Find the station with matching ID
    final stationIndex =
        _filteredStations.indexWhere((station) => station['id'] == markerId);

    if (stationIndex != -1) {
      // Cancel any pending map updates and retries to avoid conflicts
      _mapUpdateTimer?.cancel();
      _mapRetryTimer?.cancel();
      _mapRetryCount = 0; // Reset retry count for marker tap

      // Reset tracking to allow immediate update
      _lastProcessedPageIndex = -1;

      // Animate to the corresponding station card
      _pageController.animateToPage(
        stationIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onCameraPositionChanged(CameraPosition position) {
    // Handle camera position changes if needed
    debugPrint('📷 Camera position changed: ${position.target}');
  }

  void _onSearchTapped() {
    debugPrint('🔍 Search tapped - navigating to station list');

    // Animate search expansion
    _searchAnimationController.forward().then((_) {
      // Navigate to station list page
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const StationListPage(),
        ),
      ).then((_) {
        // Reset animation when returning
        _searchAnimationController.reverse();
      });
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => FilterDialog(
        selectedConnectorFilters: Map.from(_selectedConnectorFilters),
        selectedPowerOutput: _selectedPowerOutput,
        onApplyFilters: (connectorFilters, powerOutput) {
          setState(() {
            _selectedConnectorFilters.clear();
            _selectedConnectorFilters.addAll(connectorFilters);
            _selectedPowerOutput = powerOutput;
          });
          _applyFilters();
        },
      ),
    );
  }

  bool get _hasActiveFilters {
    bool hasConnectorFilter =
        _selectedConnectorFilters.values.any((selected) => selected);
    bool hasPowerOutputFilter = _selectedPowerOutput != 'All';
    return hasConnectorFilter || hasPowerOutputFilter;
  }

  // Generate user-friendly feedback message when no stations match filters - matching dashboard_screen.dart
  String _generateFilterFeedbackMessage() {
    List<String> activeFilters = [];

    // Check power output filter
    if (_selectedPowerOutput != 'All') {
      activeFilters.add('${_selectedPowerOutput} power output');
    }

    // Check connector type filters
    List<String> selectedConnectors = _selectedConnectorFilters.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();

    if (selectedConnectors.isNotEmpty) {
      if (selectedConnectors.length == 1) {
        activeFilters.add('${selectedConnectors.first} connector');
      } else {
        activeFilters.add('${selectedConnectors.join(', ')} connectors');
      }
    }

    if (activeFilters.isEmpty) {
      return 'No stations found in this area';
    } else if (activeFilters.length == 1) {
      return 'No stations found with ${activeFilters.first} in this area';
    } else {
      return 'No stations found with ${activeFilters.join(' and ')} in this area';
    }
  }

  // Clear all active filters and reload unfiltered data - matching dashboard_screen.dart
  Future<void> _clearAllFilters() async {
    setState(() {
      // Clear connector filters
      for (String key in _selectedConnectorFilters.keys) {
        _selectedConnectorFilters[key] = false;
      }
      // Reset power output filter
      _selectedPowerOutput = 'All';
      // Clear filtered results
      _filteredStations = [];
      _isSearching = false;
    });

    // Reload unfiltered data
    await _loadNearestStations();

    debugPrint('✅ All filters cleared and data reloaded');
  }

  void _onStationSelected(Map<String, dynamic> station) {
    debugPrint('🎯 Station selected from map: ${station['name']}');

    // Get station coordinates
    final double lat = station['latitude'] as double? ?? 0.0;
    final double lng = station['longitude'] as double? ?? 0.0;

    debugPrint('📍 Marker coordinates: $lat, $lng');

    if (lat != 0.0 && lng != 0.0) {
      // Load nearest stations from the tapped marker's location
      _loadNearestStationsFromLocation(lat, lng);
    } else {
      // Fallback to just finding the station in current list
      _onMarkerTapped(station['id']);
    }
  }

  /// Load nearest stations from a specific location (used when marker is tapped)
  Future<void> _loadNearestStationsFromLocation(double lat, double lng) async {
    if (!mounted) return;

    debugPrint('🔄 Loading nearest stations from marker location: $lat, $lng');

    try {
      setState(() {
        _isLoadingNearestStations = true;
        _isSearching = false;
        // Update user activity location to the tapped marker's location
        _userActivityLocation = LatLng(lat, lng);
      });

      final apiResponse = await _connectivityMonitor.executeApiCall(
        () => _stationRepository.getNearestStations(
          lat,
          lng,
          radius: _searchRadius,
        ),
        context: mounted ? context : null,
        errorMessage:
            'Unable to load nearby stations. Please check your connection.',
        showErrorOnFailure: false,
      );

      if (apiResponse.success == true && apiResponse.data != null && mounted) {
        debugPrint(
            'API returned ${apiResponse.data!.length} stations from marker location');

        final formattedStations = apiResponse.data!.map((station) {
          String status = station.status ?? 'Available';
          final connectorTypeStr = station.getConnectorTypesString();
          final connectorTypes = station.getConnectorTypes();

          // Convert ConnectorType objects to Maps for UI compatibility
          final connectorTypesMap = connectorTypes
              .map((connectorType) => {
                    'name': connectorType.name,
                    'icon': connectorType.icon,
                    'power': connectorType.power,
                    'power_type': connectorType.power,
                    'guns': connectorType.guns,
                    'available_guns': connectorType.availableGuns,
                  })
              .toList();

          double distance = station.distance ?? 0.0;
          final String extractedUid = station.uid;

          return {
            'id': station.stationId?.toString() ?? '',
            'uid': extractedUid,
            'name': station.name ?? 'Unknown Station',
            'address': station.address ?? 'Address not available',
            'latitude': station.latitude ?? 0.0,
            'longitude': station.longitude ?? 0.0,
            'status': status,
            'distance': distance,
            'connectorTypeStr': connectorTypeStr,
            'connectorTypes': connectorTypesMap,
            'types': connectorTypesMap,
          };
        }).toList();

        setState(() {
          _stations = formattedStations;
          _filteredStations = List.from(formattedStations);
          _showingNearestStations = true;
          _errorMessage = null;
        });

        debugPrint(
            '✅ Successfully loaded ${_stations.length} stations from marker location');

        // Focus on the first station in the list
        if (_filteredStations.isNotEmpty && _pageController.hasClients) {
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      } else {
        setState(() {
          _errorMessage = 'No stations found in this area';
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading nearest stations from marker location: $e');
      setState(() {
        _errorMessage = 'Failed to load stations from this location';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingStations = false;
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  void _onStationPageChanged(int index) {
    debugPrint('🔄 Station page changed to index: $index');

    // Validate index bounds
    if (index < 0 || index >= _filteredStations.length) {
      debugPrint(
          '❌ Invalid station index: $index (total: ${_filteredStations.length})');
      return;
    }

    // Cancel any pending map updates and retries to prevent conflicts
    _mapUpdateTimer?.cancel();
    _mapRetryTimer?.cancel();
    _mapRetryCount = 0; // Reset retry count for new page

    // Skip if this is the same page we just processed
    if (_lastProcessedPageIndex == index) {
      debugPrint('🔄 Skipping duplicate page change for index: $index');
      return;
    }

    final station = _filteredStations[index];
    final stationName = station['name'] ?? 'Unknown Station';
    final lat = station['latitude'] as double? ?? 0.0;
    final lng = station['longitude'] as double? ?? 0.0;

    debugPrint('🎯 Focusing map on station: $stationName');
    debugPrint('📍 Target coordinates: $lat, $lng');

    // Validate coordinates
    if (lat == 0.0 || lng == 0.0) {
      debugPrint('❌ Invalid coordinates for station: $stationName');
      return;
    }

    // Update last processed index immediately
    _lastProcessedPageIndex = index;

    // Debounce map updates to handle rapid swiping
    _mapUpdateTimer = Timer(const Duration(milliseconds: 200), () {
      _updateMapForStation(lat, lng, stationName);
    });
  }

  /// Robust map update method with proper error handling and validation
  void _updateMapForStation(double lat, double lng, String stationName) {
    if (!mounted) {
      debugPrint('❌ Widget not mounted, skipping map update');
      return;
    }

    // Validate map state
    if (_googleMapKey.currentState == null) {
      // Check retry limit to prevent infinite loops
      if (_mapRetryCount >= _maxMapRetries) {
        debugPrint(
            '❌ Max map retries reached ($_maxMapRetries), aborting map update');
        _mapRetryCount = 0; // Reset for next attempt
        return;
      }

      debugPrint(
          '❌ Google Map state is null, retry ${_mapRetryCount + 1}/$_maxMapRetries in 100ms');

      // Cancel any existing retry timer to prevent accumulation
      _mapRetryTimer?.cancel();

      _mapRetryCount++;
      _mapRetryTimer = Timer(const Duration(milliseconds: 100), () {
        if (mounted) {
          _updateMapForStation(lat, lng, stationName);
        } else {
          _mapRetryCount = 0; // Reset if widget unmounted
        }
      });
      return;
    }

    // Reset retry count on successful map state validation
    _mapRetryCount = 0;

    try {
      // Update tracking variables
      _lastFocusedLocation = LatLng(lat, lng);
      _lastMapFocusTime = DateTime.now();

      // Focus map with smooth animation
      _googleMapKey.currentState!.focusOnLocation(lat, lng, 15.0);
      debugPrint('✅ Successfully focused map on: $stationName ($lat, $lng)');
    } catch (e) {
      debugPrint('❌ Error focusing map on station: $e');
      // Reset retry count on error to allow future attempts
      _mapRetryCount = 0;
    }
  }

  /// Reset all map-related state and timers to prevent memory leaks
  void _resetMapState() {
    debugPrint('🗺️ Resetting map state and cleaning up timers...');

    // Cancel all map-related timers
    _mapUpdateTimer?.cancel();
    _mapRetryTimer?.cancel();

    // Reset state variables
    _mapRetryCount = 0;
    _lastProcessedPageIndex = -1;
    _lastFocusedLocation = null;
    _lastMapFocusTime = null;

    debugPrint('🗺️ Map state reset completed');
  }

  /// Handle app lifecycle changes to prevent map freezing during background/foreground transitions
  void _handleAppLifecycleChange() {
    debugPrint(
        '🗺️ App lifecycle changed, resetting map state to prevent freezing...');
    _resetMapState();
  }

  /// Force map recovery if it becomes unresponsive
  Future<void> _recoverMapIfNeeded() async {
    if (!mounted) return;

    try {
      debugPrint('🗺️ Attempting map recovery...');

      // Reset all map state
      _resetMapState();

      // Give the map widget time to stabilize
      await Future.delayed(const Duration(milliseconds: 300));

      // Try to refresh the current station if we have one
      if (_filteredStations.isNotEmpty && _pageController.hasClients) {
        final currentIndex = _pageController.page?.round() ?? 0;
        if (currentIndex < _filteredStations.length) {
          final station = _filteredStations[currentIndex];
          final lat = station['latitude'] as double? ?? 0.0;
          final lng = station['longitude'] as double? ?? 0.0;
          final name = station['name'] as String? ?? 'Unknown Station';

          if (lat != 0.0 && lng != 0.0) {
            debugPrint('🗺️ Recovering map focus to current station: $name');
            _updateMapForStation(lat, lng, name);
          }
        }
      }

      debugPrint('🗺️ Map recovery completed');
    } catch (e) {
      debugPrint('❌ Error during map recovery: $e');
    }
  }

  Widget _buildLoadingIndicator() {
    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 180, // Reduced height to match new layout
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading stations...',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    // Check if this is a location-related error
    final isLocationError = _errorMessage?.contains('location') == true;

    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 220, // Increased height for additional button
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isLocationError ? Icons.location_off : Icons.error_outline,
                  size: 48,
                  color: isLocationError
                      ? Colors.orange
                      : Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage ?? 'Something went wrong',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                if (isLocationError) ...[
                  // Location-specific action button
                  ElevatedButton.icon(
                    onPressed: _getCurrentLocation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    icon: const Icon(Icons.my_location, size: 20),
                    label: const Text('Enable Location'),
                  ),
                  const SizedBox(height: 12),
                  // Alternative: Browse all stations
                  TextButton(
                    onPressed: _navigateToStationList,
                    child: Text(
                      'Browse All Stations',
                      style: TextStyle(
                        color: AppThemes.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ] else ...[
                  // Generic retry button for other errors
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                      });
                      _initializeLocationAndLoadStations();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Positioned(
      bottom: 100, // Moved up to avoid navigation bar overlap
      left: 0,
      right: 0,
      child: Container(
        height: 180, // Reduced height to match new layout
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.ev_station_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
              ),
              const SizedBox(height: 16),
              Text(
                'No stations found',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Try adjusting your filters or search in a different area',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Positioned(
      right: 16,
      bottom:
          380, // Moved upward toward middle of screen for better accessibility
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: "refresh",
            onPressed: _loadNearestStations,
            backgroundColor: AppThemes.primaryColor,
            child: const Icon(Icons.refresh, color: Colors.white),
          ),
          const SizedBox(height: 12),
          FloatingActionButton(
            heroTag: "location",
            onPressed: _getCurrentLocation,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: Icon(
              Icons.my_location,
              color: AppThemes.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          FloatingActionButton(
            heroTag: "list",
            onPressed: _navigateToStationList,
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: Icon(
              Icons.list,
              color: AppThemes.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    debugPrint('🎯 Target button pressed - getting current location...');

    // Show loading state
    setState(() {
      _isLoadingNearestStations = true;
    });

    try {
      // Force location service to get fresh location
      _locationService.invalidateLocation();

      // Try to get location with extended timeout
      Position? position = await _locationService.getCurrentLocation();

      // If that fails, try the extended timeout method
      if (position == null) {
        debugPrint('🔄 First attempt failed, trying extended timeout...');
        position = await _getLocationWithExtendedTimeout();
      }

      if (position != null && mounted) {
        setState(() {
          _currentLatitude = position!.latitude;
          _currentLongitude = position.longitude;
          _userActivityLocation = LatLng(position.latitude, position.longitude);
          _hasLocationUpdate = true;
        });

        debugPrint(
            '✅ Target button: Location obtained: ${position.latitude}, ${position.longitude}');

        // Focus map on user location
        if (_googleMapKey.currentState != null) {
          _googleMapKey.currentState!.focusOnLocation(
            position.latitude,
            position.longitude,
            14.0,
          );
        }

        // Reload stations for new location
        await _loadNearestStations();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Location updated and stations refreshed'),
              backgroundColor: AppThemes.primaryColor,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        debugPrint('❌ Target button: Unable to get location');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Unable to get current location. Please check location permissions and GPS.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error getting current location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error getting location. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingNearestStations = false;
        });
      }
    }
  }

  void _navigateToStationList() {
    debugPrint('🔍 Navigating to station list page');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StationListPage(),
      ),
    );
  }

  // Load all marker API stations for map display
  Future<void> _loadAllMapMarkers() async {
    if (!mounted) return;

    setState(() {
      _isLoadingMapMarkers = true;
    });

    try {
      debugPrint('🗺️ Loading all marker API stations for map display...');

      // Get all station markers from API
      final markers = await _apiBridge.getApiStationMarkers();

      if (mounted && markers.isNotEmpty) {
        // Convert markers to map format for GoogleMapWidget
        final formattedMarkers =
            MapMarkerUtils.convertMarkersToMapFormat(markers);

        setState(() {
          _allMapStations = formattedMarkers;
          _isLoadingMapMarkers = false;
        });

        debugPrint(
            '✅ Loaded ${formattedMarkers.length} marker stations for map display');
      } else {
        setState(() {
          _allMapStations = [];
          _isLoadingMapMarkers = false;
        });
        debugPrint('⚠️ No marker stations found');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _allMapStations = [];
          _isLoadingMapMarkers = false;
        });
      }
      debugPrint('❌ Error loading marker stations: $e');
    }
  }

  Future<void> _applyFilters() async {
    // Check if any filters are selected - matching dashboard_screen.dart logic
    bool hasConnectorFilter =
        _selectedConnectorFilters.values.any((selected) => selected);
    bool hasPowerOutputFilter = _selectedPowerOutput != 'All';

    if (!hasConnectorFilter && !hasPowerOutputFilter) {
      // No filters selected, reload unfiltered data
      debugPrint('🔍 No filters selected, fetching all stations from server');
      await _loadNearestStations();
      return;
    }

    debugPrint('🔍 Applying server-side filters...');

    setState(() {
      _isLoadingStations = true;
      _isSearching = false;
    });

    try {
      final LatLng locationToFetch = _userActivityLocation ??
          LatLng(_currentLatitude ?? _indiaLatitude,
              _currentLongitude ?? _indiaLongitude);

      // Prepare filter parameters
      String? powerOutput =
          _selectedPowerOutput != 'All' ? _selectedPowerOutput : null;
      // Convert display names to standard codes for API
      List<String> selectedDisplayNames = _selectedConnectorFilters.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList();

      List<String>? selectedConnectorStandards = selectedDisplayNames.isNotEmpty
          ? FilterDialog.getStandardCodes(selectedDisplayNames)
          : null;

      // Debug logging for filter parameters
      debugPrint('🔍 Power Output Filter: $powerOutput');
      debugPrint('🔍 Selected Connector Filters: $_selectedConnectorFilters');
      debugPrint('🔍 Connector Standards for API: $selectedConnectorStandards');
      debugPrint('🔍 Selected Display Names: $selectedDisplayNames');

      if (selectedConnectorStandards?.isEmpty == true) {
        selectedConnectorStandards = null;
        debugPrint('🔍 No connector filters selected, setting to null');
      }

      // Make API call with filter parameters using connectivity monitor - matching dashboard_screen.dart
      final apiResponse = await _connectivityMonitor.executeApiCall(
        () => _stationRepository.getNearestStations(
          locationToFetch.latitude,
          locationToFetch.longitude,
          radius: _searchRadius,
          powerOutput: powerOutput,
          connectorStandards: selectedConnectorStandards,
        ),
        context: mounted ? context : null,
        errorMessage: 'Unable to apply filters. Please check your connection.',
        showErrorOnFailure: false,
      );

      debugPrint(
          '🔍 API Response: success=${apiResponse.success}, data count=${apiResponse.data?.length ?? 0}');

      if (apiResponse.success == true && apiResponse.data != null && mounted) {
        final formattedStations = apiResponse.data!.map((station) {
          String status = station.status ?? 'Available';
          final connectorTypeStr = station.getConnectorTypesString();
          final connectorTypes = station.getConnectorTypes();

          // Convert ConnectorType objects to Maps for UI compatibility
          final connectorTypesMap = connectorTypes
              .map((connectorType) => {
                    'name': connectorType.name,
                    'icon': connectorType.icon,
                    'power': connectorType.power,
                    'power_type': connectorType.power,
                    'guns': connectorType.guns,
                    'available_guns': connectorType.availableGuns,
                  })
              .toList();

          double distance = station.distance ?? 0.0;

          return {
            'id': station.stationId.toString(),
            'name': station.name,
            'latitude': station.latitude,
            'longitude': station.longitude,
            'address': station.address,
            'city': station.city,
            'uid': station.uid,
            'distance': distance,
            'status': status,
            'availability': status,
            'connectorType': connectorTypeStr,
            'connectorTypes': connectorTypesMap,
            'freeGuns': status == 'Available' ? 1 : 0,
            'mapPinUrl': status == 'Available'
                ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
                : (status == 'In Use'
                    ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                    : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
            'focusedMapPinUrl':
                'https://api2.eeil.online/mapicons/ecoplug_focus.png',
          };
        }).toList();

        setState(() {
          _filteredStations = formattedStations;
          _stations = formattedStations; // Update base stations too
          _errorMessage = null;
          _isLoadingStations = false;
        });

        // Reset page controller to first station
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }

        debugPrint(
            '✅ Filters applied successfully: ${_filteredStations.length} stations');
      } else {
        // Generate user-friendly message based on selected filters
        String filterMessage = _generateFilterFeedbackMessage();

        setState(() {
          _filteredStations = [];
          _stations = [];
          _errorMessage = filterMessage;
        });
        debugPrint(
            '❌ Server-side filtering returned no results: ${apiResponse.message}');
      }
    } catch (e) {
      setState(() {
        _filteredStations = [];
        _stations = [];
        _isLoadingStations = false;
        _errorMessage = 'Error applying filters. Please try again.';
      });
      debugPrint('❌ Error during server-side filtering: $e');
    }
  }

  void _openDirectionsToStation(Map<String, dynamic> station) {
    final stationLat = station['latitude'] as double? ?? 0.0;
    final stationLng = station['longitude'] as double? ?? 0.0;
    final stationName = station['name']?.toString() ?? 'Charging Station';
    final stationAddress = station['address']?.toString() ?? '';

    debugPrint('🎯 Station card tapped: $stationName');
    debugPrint('📍 Coordinates: $stationLat, $stationLng');
    debugPrint('🔌 Connector types: ${station['connectorTypes']}');

    // Focus map on station location with higher zoom level for better visibility
    if (stationLat != 0.0 &&
        stationLng != 0.0 &&
        _googleMapKey.currentState != null) {
      _googleMapKey.currentState!.focusOnLocation(stationLat, stationLng, 17.0);
      debugPrint(
          '🗺️ Focused map on station location: $stationLat, $stationLng');
    }

    // Open Google Maps with directions
    _openGoogleMapsDirections(
        stationLat, stationLng, stationName, stationAddress);
  }

  Future<void> _openGoogleMapsDirections(
      double lat, double lng, String stationName, String address) async {
    try {
      // Create Google Maps URL with directions
      final String googleMapsUrl =
          'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng&destination_place_id=&travelmode=driving';

      // Alternative URL with place name for better user experience
      final String encodedStationName = Uri.encodeComponent(stationName);
      final String encodedAddress = Uri.encodeComponent(address);
      final String googleMapsUrlWithName =
          'https://www.google.com/maps/dir/?api=1&destination=$encodedStationName,$encodedAddress&destination_place_id=&travelmode=driving';

      final Uri uri =
          Uri.parse(address.isNotEmpty ? googleMapsUrlWithName : googleMapsUrl);

      debugPrint('🗺️ Opening Google Maps: $uri');

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        // Fallback to basic Google Maps URL
        final Uri fallbackUri =
            Uri.parse('https://maps.google.com/?q=$lat,$lng');
        if (await canLaunchUrl(fallbackUri)) {
          await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
        } else {
          throw 'Could not launch Google Maps';
        }
      }
    } catch (e) {
      debugPrint('❌ Error opening Google Maps: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open Google Maps: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _navigateToStationDetails(Map<String, dynamic> station) {
    final String extractedUid = station['uid']?.toString() ?? '';

    debugPrint('🔒 Navigating to station details - UID: $extractedUid');

    if (extractedUid.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StationDetailsPage(uid: extractedUid),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Station details not available'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Build modern neon dark green battery icon for top-left position - copied from dashboard_screen.dart
  Widget _buildActiveSessionsBatteryIcon() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Modern neon dark green color palette
    const neonGreen = Color(0xFF00FF41); // Bright neon green
    const darkGreen = Color(0xFF0D4F3C); // Dark green base
    const glowGreen = Color(0xFF00CC33); // Glow effect green

    return Tooltip(
      message: 'Active Charging Sessions',
      child: Material(
        elevation: 8,
        shadowColor: isDarkMode
            ? neonGreen.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.2),
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: _navigateToActiveSessions,
          customBorder: const CircleBorder(),
          splashColor: neonGreen.withValues(alpha: 0.2),
          highlightColor: glowGreen.withValues(alpha: 0.1),
          child: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  darkGreen,
                  darkGreen.withValues(alpha: 0.8),
                  Colors.black.withValues(alpha: 0.9),
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
              border: Border.all(
                color: neonGreen.withValues(alpha: 0.6),
                width: 1.5,
              ),
              boxShadow: [
                // Neon glow effect
                BoxShadow(
                  color: neonGreen.withValues(alpha: 0.4),
                  blurRadius: 12,
                  spreadRadius: 3,
                ),
                // Inner glow
                BoxShadow(
                  color: glowGreen.withValues(alpha: 0.3),
                  blurRadius: 6,
                  spreadRadius: 1,
                ),
                // Depth shadow
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.5)
                      : Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Modern battery icon with neon effect
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        neonGreen.withValues(alpha: 0.1),
                        Colors.transparent,
                      ],
                      stops: const [0.0, 1.0],
                    ),
                  ),
                  child: Icon(
                    Icons.battery_charging_full,
                    color: neonGreen,
                    size: 28,
                    shadows: [
                      Shadow(
                        color: neonGreen.withValues(alpha: 0.8),
                        blurRadius: 4,
                      ),
                      Shadow(
                        color: glowGreen.withValues(alpha: 0.6),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                ),
                // Pulsing neon animation overlay
                AnimatedBuilder(
                  animation: _batteryPulseAnimation,
                  builder: (context, child) {
                    final pulseValue = _batteryPulseAnimation.value;
                    return Container(
                      width: 56 + (pulseValue * 12),
                      height: 56 + (pulseValue * 12),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: neonGreen.withValues(
                            alpha: 0.4 * (1 - pulseValue),
                          ),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: neonGreen.withValues(
                              alpha: 0.3 * (1 - pulseValue),
                            ),
                            blurRadius: 8 + (pulseValue * 4),
                            spreadRadius: pulseValue * 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),
                // Additional inner pulse for enhanced neon effect
                AnimatedBuilder(
                  animation: _batteryPulseAnimation,
                  builder: (context, child) {
                    final innerPulse = _batteryPulseAnimation.value * 0.5;
                    return Container(
                      width: 40 + (innerPulse * 8),
                      height: 40 + (innerPulse * 8),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: glowGreen.withValues(
                            alpha: 0.2 * (1 - innerPulse),
                          ),
                          width: 1,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Navigate to active sessions screen
  void _navigateToActiveSessions() {
    Navigator.pushNamed(context, '/active-sessions');
  }

  Widget _buildConnectorTypesSection(Map<String, dynamic> station) {
    // Extract connector types from the API structure
    final dynamic types = station['types'] ?? station['connectorTypes'];

    debugPrint('🔌 Building connector section for: ${station['name']}');
    debugPrint('🔌 Raw types data: $types');
    debugPrint('🔌 Types data type: ${types.runtimeType}');

    if (types == null) {
      debugPrint('🔌 No connector types found, using fallback');
      return _buildFallbackConnectorSection();
    }

    List<Map<String, dynamic>> connectors = [];

    // Handle both Map and List formats from API
    if (types is Map) {
      // API format: "types": {"0": {...}, "1": {...}}
      connectors = types.values.whereType<Map<String, dynamic>>().toList();
      debugPrint('🔌 Processed Map format: ${connectors.length} connectors');
    } else if (types is List) {
      // Handle List of Maps (converted from ConnectorType objects)
      connectors = types.whereType<Map<String, dynamic>>().toList();
      debugPrint('🔌 Processed List format: ${connectors.length} connectors');
    }

    if (connectors.isEmpty) {
      debugPrint('🔌 No valid connectors found, using fallback');
      return _buildFallbackConnectorSection();
    }

    debugPrint('🔌 Final connectors: $connectors');

    // Adopt station list sheet design - simple row without container box
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(2), // Reduced for compact layout
          decoration: BoxDecoration(
            color:
                const Color(0xFF3D7AF5).withAlpha(26), // Same as station list
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.bolt,
            color: Color(0xFF3D7AF5),
            size: 16,
          ),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            _getConnectorTypesText(connectors),
            style: const TextStyle(
              color: Color(0xFF3D7AF5),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Extract connector types text from connectors list (matching station list sheet logic)
  String _getConnectorTypesText(List<Map<String, dynamic>> connectors) {
    if (connectors.isEmpty) {
      return 'Various';
    }

    Set<String> connectorNames = {};
    for (var connector in connectors) {
      final name = connector['name']?.toString();
      if (name != null && name.isNotEmpty) {
        connectorNames.add(name);
      }
    }

    return connectorNames.isNotEmpty ? connectorNames.join(', ') : 'Various';
  }

  Widget _buildFallbackConnectorSection() {
    // Adopt station list sheet design for fallback as well
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: const Color(0xFF3D7AF5).withAlpha(26),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.bolt,
            color: Color(0xFF3D7AF5),
            size: 16,
          ),
        ),
        const SizedBox(width: 4),
        const Expanded(
          child: Text(
            'Charging Available',
            style: TextStyle(
              color: Color(0xFF3D7AF5),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
