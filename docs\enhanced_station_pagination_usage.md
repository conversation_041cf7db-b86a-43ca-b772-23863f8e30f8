# Enhanced Station Pagination with Dynamic URL Construction

## Overview

The enhanced connectivity service now supports dynamic URL construction for station pagination API endpoints with location and power output parameters. This provides robust connectivity handling, intelligent retry logic, and proper parameter management.

## Key Features

### Dynamic URL Construction
- **Base Endpoint**: `https://api2.eeil.online/api/v1/user/station/paginate`
- **Conditional Parameters**: Only includes parameters when they have valid values
- **Proper URL Encoding**: Handles special characters and spaces correctly
- **Backward Compatibility**: Maintains existing pagination functionality

### Enhanced Connectivity Handling
- **Connectivity Checks**: Verifies internet connection before API calls
- **Intelligent Retry**: Exponential backoff with configurable retry attempts
- **Error Handling**: Comprehensive error management with user feedback
- **Quality Monitoring**: Connection quality assessment and adaptation

## API Parameters

### Always Included
- `page`: Current page number (default: 1)
- `limit`: Number of results per page (default: 20)

### Conditional Parameters
- `latitude`: User's latitude (only when location is available)
- `longitude`: User's longitude (only when location is available)
- `power_output`: "AC" or "DC" (excluded when value is "All")
- `standard`: Connector standards (comma-separated list)

## Usage Examples

### Basic Usage with Enhanced Connectivity

```dart
import 'package:ecoplug/services/connectivity_monitor.dart';
import 'package:ecoplug/features/station/services/station_service.dart';

class StationListController {
  final ConnectivityMonitor _connectivityMonitor = ConnectivityMonitor();
  final StationService _stationService = StationService();

  Future<void> loadStations({
    int page = 1,
    int limit = 20,
    double? latitude,
    double? longitude,
    String? powerOutput,
    List<String>? connectorStandards,
  }) async {
    try {
      final result = await _connectivityMonitor.executeStationPaginationCall(
        () => _stationService.fetchStationsPaginated(
          page: page,
          limit: limit,
          latitude: latitude,
          longitude: longitude,
          powerOutput: powerOutput,
          connectorStandards: connectorStandards,
        ),
        context: context,
        errorMessage: 'Unable to load stations. Please check your connection.',
      );

      // Handle successful response
      if (result.success == true && result.data != null) {
        _updateStationList(result.data!);
      }
    } catch (e) {
      // Error handling is managed by the connectivity monitor
      debugPrint('Failed to load stations: $e');
    }
  }
}
```

### Advanced Usage with Filtering

```dart
Future<void> loadFilteredStations() async {
  // Example: Load stations near user with AC power output and specific connectors
  await loadStations(
    page: 1,
    limit: 20,
    latitude: 18.3236,           // User's current latitude
    longitude: 73.88075,         // User's current longitude
    powerOutput: 'AC',           // Only AC charging stations
    connectorStandards: [        // Specific connector types
      'IEC_60309_2_three_16',    // 3 Pin connector
      'IEC_62196_Type_2',        // Type 2 connector
    ],
  );
}
```

### URL Construction Examples

The system automatically constructs URLs based on provided parameters:

```dart
// Basic pagination only
// URL: https://api2.eeil.online/api/v1/user/station/paginate?page=1&limit=20

// With user location
// URL: https://api2.eeil.online/api/v1/user/station/paginate?page=1&limit=20&latitude=18.3236&longitude=73.88075

// With power output filter
// URL: https://api2.eeil.online/api/v1/user/station/paginate?page=1&limit=20&latitude=18.3236&longitude=73.88075&power_output=AC

// With connector standards
// URL: https://api2.eeil.online/api/v1/user/station/paginate?page=1&limit=20&latitude=18.3236&longitude=73.88075&power_output=AC&standard=IEC_60309_2_three_16,IEC_62196_Type_2

// Note: "All" power output is excluded (no power_output parameter sent)
// URL: https://api2.eeil.online/api/v1/user/station/paginate?page=1&limit=20&latitude=18.3236&longitude=73.88075
```

## Error Handling

### Connectivity Errors
The enhanced connectivity service automatically handles:
- Network timeouts
- Connection loss during requests
- DNS resolution failures
- Server unavailability

### User Feedback
- Automatic retry with exponential backoff
- User-friendly error messages
- Retry buttons in error notifications
- Graceful degradation when offline

## Best Practices

### 1. Always Use Enhanced Connectivity Monitor
```dart
// ✅ Good - Uses enhanced connectivity handling
await _connectivityMonitor.executeStationPaginationCall(() => apiCall);

// ❌ Avoid - Direct API calls without connectivity handling
await _stationService.fetchStationsPaginated();
```

### 2. Provide Context for Error Handling
```dart
await _connectivityMonitor.executeStationPaginationCall(
  () => apiCall,
  context: context, // Enables user feedback
  errorMessage: 'Custom error message',
);
```

### 3. Handle Location Gracefully
```dart
// Only pass location when available
final latitude = _userLocation?.latitude;
final longitude = _userLocation?.longitude;

await loadStations(
  latitude: latitude,
  longitude: longitude,
  // Other parameters...
);
```

### 4. Filter Parameters Correctly
```dart
// Exclude "All" values
final powerOutput = _selectedPowerOutput != 'All' ? _selectedPowerOutput : null;

// Only pass non-empty connector lists
final connectors = _selectedConnectors.isNotEmpty ? _selectedConnectors : null;
```

## Migration Guide

### From Old Manual URL Construction
```dart
// Old approach - manual URL building
String queryParams = 'page=$page&limit=$limit';
if (filters != null) {
  filters.forEach((key, value) {
    if (value != null && value.toString().isNotEmpty) {
      queryParams += '&$key=${Uri.encodeComponent(value.toString())}';
    }
  });
}
final response = await _apiService.get('station/paginate?$queryParams');

// New approach - enhanced service with dynamic URL construction
final response = await _connectivityMonitor.executeStationPaginationCall(
  () => _stationService.fetchStationsPaginated(
    page: page,
    limit: limit,
    latitude: latitude,
    longitude: longitude,
    powerOutput: powerOutput,
    connectorStandards: connectorStandards,
  ),
);
```

## Configuration

### Retry Settings
The enhanced connectivity service uses these default settings:
- **Max Retries**: 3 attempts
- **Base Delay**: 1 second
- **Backoff Multiplier**: 2.0 (exponential)
- **Connection Timeout**: 10 seconds

### Customization
```dart
await _connectivityService.executeStationPaginationCall(
  apiCall,
  errorMessage: 'Custom error message',
  maxRetries: 5, // Custom retry count
);
```
