# Promo Code Minimum Amount Validation Implementation

## Overview
This implementation adds comprehensive validation for promo code minimum amount requirements in the Add Balance sheet, ensuring users can only select and use promo codes when they meet the minimum amount criteria.

## Key Features Implemented

### 1. Promo Code Selection Validation
- **Disabled State**: Promo codes with unmet minimum requirements are visually disabled
- **Visual Indicators**: Orange warning badges show "Add ₹X" instead of "SAVE ₹X" for disabled promos
- **Interactive Feedback**: Tapping disabled promos shows helpful warning messages

### 2. Amount Input Validation
- **Real-time Indicators**: Shows status of applied promo code minimum requirements
- **Success Indicator**: Green checkmark when minimum amount is met
- **Warning Indicator**: Orange warning with amount needed and quick-set button
- **Auto-fill Feature**: Users can quickly set the minimum required amount

### 3. Add Balance Button Validation
- **Pre-payment Check**: Validates promo code minimum before processing payment
- **Clear Error Messages**: Shows specific error when minimum amount is not met
- **Prevents Invalid Transactions**: Blocks payment until requirements are satisfied

### 4. User Experience Enhancements
- **Helpful Warnings**: Clear messages explaining what users need to do
- **Quick Actions**: One-tap buttons to set minimum required amounts
- **Visual Feedback**: Color-coded indicators for different states

## Technical Implementation Details

### Modified Files
- `lib/screens/wallet/add_balance_sheet.dart` - Main implementation

### Key Methods Added

#### `_buildPromoMinimumIndicator()`
```dart
// Shows real-time status of promo code minimum requirements
// - Green success indicator when minimum is met
// - Orange warning with amount needed when not met
// - Quick-set button for easy amount adjustment
```

#### `_showMinimumAmountWarning()`
```dart
// Displays helpful warning when users try to select invalid promo codes
// - Shows amount needed to meet minimum
// - Provides quick-set action button
// - Available in both main sheet and promo selection sheet
```

#### Enhanced `_handleAddBalance()`
```dart
// Added validation for promo code minimum requirements
// - Checks applied promo code minimum before payment
// - Shows clear error message if minimum not met
// - Prevents invalid payment attempts
```

#### Updated `_buildPromoCodeCard()`
```dart
// Enhanced promo code cards with validation logic
// - Calculates if promo code can be selected
// - Shows appropriate visual indicators
// - Handles disabled state interactions
```

### Validation Logic

#### Promo Code Selectability
```dart
final bool canSelect = widget.currentAmount >= promo.minimumAmountApplicable;
final bool isDisabled = !canSelect && promo.minimumAmountApplicable > 0;
```

#### Visual State Management
- **Enabled**: Full color, clickable, shows "SAVE ₹X" badge
- **Disabled**: Muted colors, shows "Add ₹X" warning badge
- **Applied**: Success indicators and bonus amount display

#### Payment Validation
```dart
if (_isPromoApplied && _verifiedPromo != null) {
  final minRequired = _verifiedPromo!['minimum_amount_applicable'] ?? 0;
  if (minRequired > 0 && amount < minRequired) {
    // Show error and prevent payment
  }
}
```

## User Flow Examples

### Scenario 1: Promo Code Requires ₹500 Minimum
1. User enters ₹200 in amount field
2. User browses promo codes
3. ₹500 minimum promo shows orange "Add ₹300" badge
4. Tapping promo shows warning: "Add ₹300 more to use this promo code"
5. User can tap "Set ₹500" to auto-fill minimum amount
6. System shows green success indicator when ₹500 is entered
7. User can now select and apply the promo code

### Scenario 2: Applied Promo Code Validation
1. User applies promo code requiring ₹100 minimum
2. User enters ₹75 in amount field
3. Orange warning appears: "Add ₹25 more to use this promo code"
4. Add Balance button shows validation error if user tries to proceed
5. User increases amount to ₹100 or more
6. Green success indicator appears
7. Add Balance button allows payment to proceed

## Benefits

### For Users
- **Clear Guidance**: Always know what's required to use promo codes
- **No Confusion**: Can't accidentally select invalid promo codes
- **Quick Actions**: Easy buttons to set correct amounts
- **Visual Feedback**: Color-coded indicators for all states

### For Business
- **Prevents Errors**: No invalid promo code applications
- **Better UX**: Reduced user frustration and support tickets
- **Compliance**: Ensures promo code terms are properly enforced
- **Analytics**: Clear tracking of promo code usage patterns

## Testing Scenarios

### Automated Tests
- Promo code selection validation
- Amount input validation
- Add Balance button validation
- Visual indicator states

### Manual Testing
- Try selecting promo codes with various minimum amounts
- Test auto-fill functionality
- Verify error messages and warnings
- Check visual states and transitions

## Future Enhancements

### Potential Improvements
- **Smart Suggestions**: Recommend optimal amounts for multiple promo codes
- **Promo Code Stacking**: Support for multiple compatible promo codes
- **Dynamic Minimums**: Server-side minimum amount adjustments
- **Usage Analytics**: Track promo code selection patterns

### Performance Optimizations
- **Lazy Loading**: Load promo code details on demand
- **Caching**: Cache promo code validation results
- **Debouncing**: Optimize real-time validation checks

## Conclusion

This implementation provides a comprehensive solution for promo code minimum amount validation, ensuring users have a smooth and error-free experience when adding balance with promotional offers. The validation is enforced at multiple levels (selection, input, and payment) with clear visual feedback and helpful guidance throughout the user journey.