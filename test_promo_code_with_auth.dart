import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Test script to verify promo code API with authentication
/// This script tests the corrected implementation with proper JSON body format
class PromoCodeAuthTester {
  static const String baseUrl = 'https://api2.eeil.online/api/v1';
  static const String verifyEndpoint = '$baseUrl/user/promocodes/verify';
  
  // Test promo codes
  static const List<String> testPromoCodes = [
    'WELCOME10',
    'SAVE15', 
    'BONUS20',
    'FIRST25',
    'SERVER15',
    'TEST123',  // Invalid code for testing
  ];

  /// Main test runner
  static Future<void> runAuthTests() async {
    print('🔔 ===== PROMO CODE API AUTHENTICATION TESTING =====');
    print('🔔 Testing API: $verifyEndpoint');
    print('🔔 ================================================\n');

    // Test 1: Test with corrected JSON body format
    await testCorrectedJsonFormat();

    // Test 2: Test different payload field names
    await testDifferentFieldNames();

    // Test 3: Simulate authentication scenarios
    await testAuthenticationScenarios();

    print('\n🔔 ===== AUTH TESTS COMPLETED =====');
  }

  /// Test 1: Test with corrected JSON body format (no query parameters)
  static Future<void> testCorrectedJsonFormat() async {
    print('🧪 TEST 1: Corrected JSON Body Format');
    print('=' * 50);

    for (final code in testPromoCodes.take(3)) {
      print('🎫 Testing promo code: $code');
      
      final payload = {'code': code};
      
      try {
        final response = await http.post(
          Uri.parse(verifyEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            // Note: No auth token - will get 401, but we can see the correct format
          },
          body: jsonEncode(payload),
        ).timeout(Duration(seconds: 10));
        
        print('📊 Status: ${response.statusCode}');
        print('📥 Response: ${response.body}');
        
        if (response.statusCode == 401) {
          print('✅ EXPECTED: Authentication required (401)');
        } else if (response.statusCode == 200) {
          print('✅ SUCCESS: Promo code verification worked!');
        }
        
      } catch (e) {
        print('❌ Error: $e');
      }
      print('');
    }
  }

  /// Test 2: Test different field names to find the correct one
  static Future<void> testDifferentFieldNames() async {
    print('🧪 TEST 2: Different JSON Field Names');
    print('=' * 50);
    
    final testCode = 'SAVE15';
    final fieldFormats = [
      {'code': testCode},                    // Format 1 - Our current format
      {'promo': testCode},                   // Format 2  
      {'promo_code': testCode},              // Format 3
      {'promocode': testCode},               // Format 4
      {'coupon_code': testCode},             // Format 5
    ];
    
    for (int i = 0; i < fieldFormats.length; i++) {
      final payload = fieldFormats[i];
      print('📤 Testing Field Format ${i + 1}: ${jsonEncode(payload)}');
      
      try {
        final response = await http.post(
          Uri.parse(verifyEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode(payload),
        ).timeout(Duration(seconds: 10));
        
        print('📊 Status: ${response.statusCode}');
        print('📥 Response: ${response.body}');
        
        if (response.statusCode == 401) {
          print('✅ EXPECTED: Authentication required (401)');
        } else if (response.statusCode == 400) {
          print('❌ BAD REQUEST: Field name might be incorrect');
        } else if (response.statusCode == 200) {
          print('✅ SUCCESS: Field format ${i + 1} works!');
        }
        
      } catch (e) {
        print('❌ Error with format ${i + 1}: $e');
      }
      print('');
    }
  }

  /// Test 3: Test authentication scenarios
  static Future<void> testAuthenticationScenarios() async {
    print('🧪 TEST 3: Authentication Scenarios');
    print('=' * 50);
    
    final payload = {'code': 'SAVE15'};
    
    // Test without auth
    print('📤 Testing without authentication...');
    await makeAuthTestRequest(payload, null);
    
    // Test with dummy tokens
    final dummyTokens = [
      'Bearer dummy_token_123',
      'Bearer test_token_456',
      'Bearer invalid_token',
    ];
    
    for (final token in dummyTokens) {
      print('📤 Testing with dummy token: ${token.substring(0, 20)}...');
      await makeAuthTestRequest(payload, token);
    }
    
    print('📝 NOTE: To test with a real token, you would need to:');
    print('   1. Log in through the app');
    print('   2. Extract the auth token from SharedPreferences');
    print('   3. Use that token in the Authorization header');
    print('');
  }

  /// Helper method to make auth test requests
  static Future<void> makeAuthTestRequest(Map<String, dynamic> payload, String? authHeader) async {
    try {
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
      
      if (authHeader != null) {
        headers['Authorization'] = authHeader;
      }
      
      final response = await http.post(
        Uri.parse(verifyEndpoint),
        headers: headers,
        body: jsonEncode(payload),
      ).timeout(Duration(seconds: 10));
      
      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body.length > 200 ? response.body.substring(0, 200) + "..." : response.body}');
      
      if (response.statusCode == 401) {
        print('✅ EXPECTED: Authentication required');
      } else if (response.statusCode == 200) {
        print('✅ SUCCESS: Authentication worked!');
      }
      
    } catch (e) {
      print('❌ Request error: $e');
    }
    print('');
  }

  /// Test with a real auth token (if provided)
  static Future<void> testWithRealToken(String authToken) async {
    print('🧪 BONUS TEST: Testing with Real Auth Token');
    print('=' * 50);
    
    for (final code in testPromoCodes.take(2)) {
      print('🎫 Testing promo code: $code');
      
      final payload = {'code': code};
      
      try {
        final response = await http.post(
          Uri.parse(verifyEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: jsonEncode(payload),
        ).timeout(Duration(seconds: 10));
        
        print('📊 Status: ${response.statusCode}');
        print('📥 Response: ${response.body}');
        
        if (response.statusCode == 200) {
          try {
            final responseData = jsonDecode(response.body);
            if (responseData['success'] == true) {
              print('✅ SUCCESS: $code is valid!');
              if (responseData['data'] != null) {
                print('💰 Credits: ${responseData['data']['credits']}');
                print('💵 Min Amount: ${responseData['data']['minimum_amount_applicable']}');
              }
            } else {
              print('❌ FAILED: ${responseData['message'] ?? 'Invalid code'}');
            }
          } catch (e) {
            print('❌ JSON Parse Error: $e');
          }
        } else if (response.statusCode == 401) {
          print('❌ FAILED: Token is invalid or expired');
        }
        
      } catch (e) {
        print('❌ Request error for $code: $e');
      }
      print('');
    }
  }
}

/// Main function to run the tests
void main(List<String> args) async {
  await PromoCodeAuthTester.runAuthTests();
  
  // If a token is provided as command line argument, test with it
  if (args.isNotEmpty) {
    final token = args[0];
    print('\n🔑 Testing with provided auth token...');
    await PromoCodeAuthTester.testWithRealToken(token);
  }
}
