import 'package:flutter/material.dart';
import 'package:ecoplug/utils/app_themes.dart';
import 'dashboard_horizontal_cards.dart';
import 'dashboard_screen.dart';

/// Demo page to showcase both dashboard variants
class DashboardDemo extends StatefulWidget {
  const DashboardDemo({super.key});

  @override
  State<DashboardDemo> createState() => _DashboardDemoState();
}

class _DashboardDemoState extends State<DashboardDemo> {
  bool _showHorizontalCards = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _showHorizontalCards 
              ? 'Horizontal Cards Dashboard'
              : 'Original Bottom Sheet Dashboard',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.with<PERSON><PERSON><PERSON>(25),
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _showHorizontalCards = !_showHorizontalCards;
                });
              },
              icon: Icon(
                _showHorizontalCards 
                    ? Icons.view_list 
                    : Icons.view_carousel,
                color: AppThemes.primaryColor,
              ),
              tooltip: _showHorizontalCards 
                  ? 'Switch to Bottom Sheet View'
                  : 'Switch to Horizontal Cards View',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Info banner
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppThemes.primaryColor.withAlpha(25),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppThemes.primaryColor.withAlpha(76),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppThemes.primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Dashboard Variant Demo',
                      style: TextStyle(
                        color: AppThemes.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _showHorizontalCards
                      ? 'Horizontal Cards: Swipe left/right through station cards. Tap cards to focus map. Modern, minimalist design with smooth animations.'
                      : 'Original Bottom Sheet: Vertical scrolling station list in draggable bottom sheet. Established design with proven UX.',
                  style: TextStyle(
                    color: AppThemes.primaryColor.withAlpha(204),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // Dashboard content
          Expanded(
            child: _showHorizontalCards 
                ? const DashboardHorizontalCards()
                : const DashboardScreen(),
          ),
        ],
      ),
    );
  }
}

/// Quick access widget for testing dashboard variants
class DashboardQuickSwitcher extends StatelessWidget {
  const DashboardQuickSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Variants'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Text(
              'Choose Dashboard Style',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Horizontal Cards Option
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: InkWell(
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DashboardHorizontalCards(),
                    ),
                  );
                },
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      Icon(
                        Icons.view_carousel,
                        size: 48,
                        color: AppThemes.primaryColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Horizontal Cards',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Modern design with horizontal sliding station cards. Swipe through stations with smooth animations.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Original Bottom Sheet Option
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: InkWell(
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DashboardScreen(),
                    ),
                  );
                },
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      Icon(
                        Icons.view_list,
                        size: 48,
                        color: AppThemes.primaryColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Bottom Sheet (Original)',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Established design with draggable bottom sheet containing vertical station list. Proven user experience.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Demo Button
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DashboardDemo(),
                  ),
                );
              },
              icon: const Icon(Icons.compare),
              label: const Text('Compare Both Variants'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppThemes.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
