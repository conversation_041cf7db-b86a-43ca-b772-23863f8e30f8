# Project Structure

## Root Directory Organization

```
ecoplug/
├── lib/                    # Main application source code
├── test/                   # Unit and integration tests
├── android/                # Android-specific configuration
├── ios/                    # iOS-specific configuration
├── web/                    # Web platform support
├── assets/                 # Static assets (images, icons, data)
├── docs/                   # Project documentation
├── scripts/                # Build and deployment scripts
└── .kiro/                  # Kiro AI assistant configuration
```

## Core Application Structure (`lib/`)

### Architecture Layers

```
lib/
├── main.dart              # Application entry point
├── firebase_options.dart  # Firebase configuration
├── screens/               # UI screens and pages
├── widgets/               # Reusable UI components
├── services/              # Business logic and external integrations
├── providers/             # Riverpod state management
├── models/                # Data models and DTOs
├── repositories/          # Data access layer
├── utils/                 # Helper functions and utilities
├── config/                # App configuration
├── constants/             # App constants
└── debug/                 # Development and debugging tools
```

### Key Directories

#### `/screens/` - UI Screens
- **Organized by feature**: `auth/`, `dashboard/`, `charging/`, `wallet/`, `Trip/`, `Profile/`
- **Screen naming**: `*_screen.dart` or `*_page.dart`
- **Riverpod integration**: Prefer `*_riverpod.dart` for new screens

#### `/services/` - Business Logic
- **API services**: `api_service.dart`, `unified_api_service.dart`
- **Feature services**: `charging_*_service.dart`, `payment/*_service.dart`
- **Infrastructure**: `connectivity_*.dart`, `notification_*.dart`
- **Firebase**: `fcm_*.dart`, `messaging_service.dart`

#### `/providers/` - State Management
- **Core providers**: `providers.dart` (main provider exports)
- **Feature providers**: `*_provider.dart` for specific features
- **Notifiers**: Use Riverpod notifiers for complex state

#### `/models/` - Data Models
- **API models**: Match backend response structure
- **Feature models**: Organized in subdirectories (`auth/`, `station/`, `wallet/`)
- **Converters**: `*_converter.dart` for data transformation

#### `/widgets/` - Reusable Components
- **Common widgets**: Navigation, buttons, forms
- **Feature widgets**: `charging/`, organized by feature area
- **Naming**: Descriptive names like `station_card.dart`

#### `/utils/` - Utilities
- **Themes**: `app_themes.dart`, `app_colors.dart`
- **Helpers**: `helpers.dart`, `formatter_utils.dart`
- **Constants**: `api_constants.dart`, `constants.dart`

## Asset Organization

```
assets/
├── images/                # App images and logos
├── icons/                 # Custom icons and markers
├── data/                  # JSON data files
├── markers/               # Map marker assets
└── RFID_CARDS/           # RFID card images
```

## Testing Structure

```
test/
├── unit/                  # Unit tests (if organized)
├── integration/           # Integration tests
├── performance/           # Performance tests
└── *_test.dart           # Test files matching lib/ structure
```

## Configuration Files

- **`pubspec.yaml`**: Dependencies and asset configuration
- **`analysis_options.yaml`**: Code analysis rules (uses flutter_lints)
- **`.gitignore`**: Version control exclusions
- **`firebase_options.dart`**: Firebase platform configuration

## Development Guidelines

### File Naming Conventions
- **Screens**: `feature_screen.dart` or `feature_page.dart`
- **Services**: `feature_service.dart`
- **Models**: `feature_model.dart`
- **Providers**: `feature_provider.dart`
- **Widgets**: `descriptive_widget_name.dart`

### Import Organization
1. Dart/Flutter imports first
2. Third-party package imports
3. Local app imports (relative paths)

### Directory Guidelines
- **Feature-based organization**: Group related files by feature when possible
- **Shared components**: Place in appropriate shared directories (`widgets/`, `utils/`)
- **Platform-specific**: Use platform directories (`android/`, `ios/`) for native code

### State Management Patterns
- **New features**: Use Riverpod providers and notifiers
- **Legacy code**: Provider pattern (being migrated)
- **Global state**: Core providers in `providers/providers.dart`
- **Feature state**: Feature-specific providers

### Service Layer Patterns
- **API services**: Centralized in `services/api/`
- **Feature services**: Business logic for specific features
- **Infrastructure services**: Cross-cutting concerns (connectivity, notifications)
- **Dependency injection**: Use service locator pattern where needed