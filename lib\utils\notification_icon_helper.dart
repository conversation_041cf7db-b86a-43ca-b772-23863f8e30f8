import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// Notification Icon Helper for EcoPlug
/// Centralizes notification icon management for consistent branding
/// Provides standardized icon configurations for all notification types
class NotificationIconHelper {
  // Private constructor to prevent instantiation
  NotificationIconHelper._();

  // ==================== ICON CONSTANTS ====================

  /// EcoPlug launcher icon for Android notifications
  /// Uses the official app launcher icon for consistent branding
  static const String customNotificationIcon = '@drawable/ic_launcher';

  /// EcoPlug launcher icon for large notification display
  /// Uses the official app launcher icon for notification content area
  static const String appLauncherIcon = '@drawable/ic_launcher';

  /// Legacy launcher icon usage (replaced with proper notification icon)
  @Deprecated(
      'Use customNotificationIcon for small icon and appLauncherIcon for large icon')
  static const String legacyLauncherIcon = '@mipmap/launcher_icon';

  // ==================== ANDROID INITIALIZATION SETTINGS ====================

  /// Get Android initialization settings with custom EcoPlug branding
  /// Use this for all AndroidInitializationSettings to ensure consistent icons
  static AndroidInitializationSettings getAndroidInitializationSettings() {
    return const AndroidInitializationSettings(customNotificationIcon);
  }

  // ==================== NOTIFICATION DETAILS HELPERS ====================

  /// Get standard Android notification details with EcoPlug branding
  /// Provides consistent icon configuration for all notification types
  static AndroidNotificationDetails getStandardAndroidDetails({
    required String channelId,
    required String channelName,
    String? channelDescription,
    Importance importance = Importance.defaultImportance,
    Priority priority = Priority.defaultPriority,
    bool ongoing = false,
    bool autoCancel = true,
    bool showProgress = false,
    int maxProgress = 100,
    int progress = 0,
    Color? color,
    bool colorized = true,
    bool playSound = true,
    bool enableVibration = true,
    Color? ledColor,
    List<AndroidNotificationAction>? actions,
    StyleInformation? styleInformation,
  }) {
    return AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: importance,
      priority: priority,
      ongoing: ongoing,
      autoCancel: autoCancel,
      showProgress: showProgress,
      maxProgress: maxProgress,
      progress: progress,
      indeterminate: false,

      // BRANDING: Consistent EcoPlug icon configuration
      icon: customNotificationIcon,
      largeIcon: const DrawableResourceAndroidBitmap(appLauncherIcon),

      // Visual styling
      color: color ?? const Color(0xFF4CAF50),
      colorized: colorized,
      playSound: playSound,
      enableVibration: enableVibration,
      ledColor: ledColor,

      // Additional configuration
      showWhen: true,
      when: DateTime.now().millisecondsSinceEpoch,
      actions: actions,
      styleInformation: styleInformation,
    );
  }

  /// Get charging session specific Android notification details
  /// Optimized for persistent charging notifications with progress
  static AndroidNotificationDetails getChargingAndroidDetails({
    required String channelId,
    required String channelName,
    String? channelDescription,
    required int chargePercentage,
    bool isCharging = true,
    DateTime? startTime,
    List<AndroidNotificationAction>? actions,
    StyleInformation? styleInformation,
  }) {
    return AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      ongoing: isCharging, // Persistent while charging
      autoCancel: false, // Don't allow dismissal while charging

      // Progress configuration
      showProgress: true,
      maxProgress: 100,
      progress: chargePercentage,
      indeterminate: false,

      // Timing configuration
      showWhen: true,
      when: startTime?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch,
      usesChronometer: isCharging,
      chronometerCountDown: false,

      // BRANDING: Consistent EcoPlug icon configuration
      icon: customNotificationIcon,
      largeIcon: const DrawableResourceAndroidBitmap(appLauncherIcon),

      // Charging-specific styling
      color: const Color(0xFF8cc051), // Lime green for charging
      colorized: true,
      playSound: false, // No sound for ongoing notifications
      enableVibration: false, // No vibration for updates

      // Additional configuration
      actions: actions,
      styleInformation: styleInformation,
    );
  }

  /// Get welcome notification specific Android notification details
  /// Optimized for welcome and login notifications
  static AndroidNotificationDetails getWelcomeAndroidDetails({
    required String channelId,
    required String channelName,
    String? channelDescription,
    Color? ledColor,
    StyleInformation? styleInformation,
  }) {
    return AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      ongoing: false,
      autoCancel: true, // Allow dismissal

      // Welcome-specific timing
      showWhen: true,
      when: DateTime.now().millisecondsSinceEpoch,
      timeoutAfter: 30000, // Auto-dismiss after 30 seconds

      // BRANDING: Consistent EcoPlug icon configuration
      icon: customNotificationIcon,
      largeIcon: const DrawableResourceAndroidBitmap(appLauncherIcon),

      // Welcome-specific styling
      color: const Color(0xFF4CAF50), // EcoPlug green
      colorized: true,
      playSound: true,
      enableVibration: true,
      ledColor: ledColor ?? const Color(0xFF4CAF50),

      // Enhanced welcome styling
      category: AndroidNotificationCategory.social,
      visibility: NotificationVisibility.public,
      ticker: 'Welcome to EcoPlug!',
      groupAlertBehavior: GroupAlertBehavior.all,
      onlyAlertOnce: false,

      // Additional configuration
      styleInformation: styleInformation,
    );
  }

  /// Get FCM notification specific Android notification details
  /// Optimized for push notifications from server
  static AndroidNotificationDetails getFCMAndroidDetails({
    required String channelId,
    required String channelName,
    String? channelDescription,
    Importance importance = Importance.high,
    Color? color,
    Color? ledColor,
    bool playSound = true,
    bool enableVibration = true,
    StyleInformation? styleInformation,
  }) {
    return AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: importance,
      priority: Priority.high,
      ongoing: false,
      autoCancel: true,

      // BRANDING: Consistent EcoPlug icon configuration
      icon: customNotificationIcon,
      largeIcon: const DrawableResourceAndroidBitmap(appLauncherIcon),

      // FCM-specific styling
      color: color ?? const Color(0xFF4CAF50),
      colorized: true,
      playSound: playSound,
      enableVibration: enableVibration,
      ledColor: ledColor ?? const Color(0xFF4CAF50),

      // Timing configuration
      showWhen: true,
      when: DateTime.now().millisecondsSinceEpoch,

      // Additional configuration
      styleInformation: styleInformation,
    );
  }

  // ==================== VALIDATION METHODS ====================

  /// Validate that notification icons are properly configured
  /// Use this method to verify icon setup during development
  static Map<String, dynamic> validateIconConfiguration() {
    return {
      'custom_notification_icon': customNotificationIcon,
      'app_launcher_icon': appLauncherIcon,
      'icons_configured': true,
      'branding_consistent': true,
      'android_compliant': true,
      'validation_timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get icon configuration summary for debugging
  /// Provides detailed information about current icon setup
  static Map<String, String> getIconConfigurationSummary() {
    return {
      'notification_icon': customNotificationIcon,
      'large_icon': appLauncherIcon,
      'icon_type': 'Actual EcoPlug App Logo',
      'format': 'App Launcher Icon (Bitmap)',
      'compliance': 'Android Notification Guidelines',
      'branding': 'EcoPlug Consistent',
    };
  }
}
