import 'package:flutter/material.dart';
import 'dart:math' as math; // Alias for math library
import 'dart:async';
import 'package:google_maps_flutter/google_maps_flutter.dart'; // Import for LatLng
import '../../models/station/station_marker_response.dart';
import '../../services/api_bridge.dart';
import '../../utils/map_marker_utils.dart';
import 'google_map_widget.dart';

class DashboardWithApiMarkers extends StatefulWidget {
  const DashboardWithApiMarkers({super.key});

  @override
  State<DashboardWithApiMarkers> createState() =>
      _DashboardWithApiMarkersState();
}

class _DashboardWithApiMarkersState extends State<DashboardWithApiMarkers> {
  final ApiBridge _apiBridge = ApiBridge();
  List<Map<String, dynamic>> _stationMarkers = [];
  bool _isLoading = true;
  String? _error;
  Map<String, dynamic>? _selectedStation;
  bool _showStationDetails = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _preloadMapResources();
      await _fetchData();
      _startPeriodicRefresh();
    });
  }

  Future<void> _preloadMapResources() async {
    try {
      if (!mounted) return;
      await Future.wait([
        precacheImage(
            const NetworkImage(
                'https://api2.eeil.online/mapicons/ecoplug_default.png'),
            context),
        precacheImage(
            const NetworkImage(
                'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png'),
            context),
        precacheImage(
            const NetworkImage(
                'https://api2.eeil.online/icons/unavailable.png'),
            context),
      ]);
    } catch (e) {
      debugPrint('Error preloading map resources: $e');
    }
  }

  Future<void> _fetchData() async {
    int maxAttempts = 3;
    int attempt = 1;
    while (attempt <= maxAttempts) {
      setState(() {
        _isLoading = true;
        _error = null;
      });
      try {
        final markersFuture = _apiBridge.getApiStationMarkers();
        final results = await Future.wait([markersFuture]);
        final markers = results[0] as List<dynamic>;
        final formattedMarkers = MapMarkerUtils.convertMarkersToMapFormat(
            markers.cast<StationMarkerData>());
        setState(() {
          _stationMarkers = formattedMarkers;
          _isLoading = false;
        });

        // Precache images only if the widget is still mounted
        if (mounted) {
          for (var marker in formattedMarkers) {
            final url = marker['mapPinUrl'] as String?;
            if (url != null && url.isNotEmpty) {
              precacheImage(NetworkImage(url), context);
            }
          }
        }
        debugPrint('Fetched ${markers.length} markers from API');
        return; // Success
      } catch (e) {
        if (attempt < maxAttempts) {
          int delay =
              (500 * math.pow(2, attempt - 1)).toInt(); // Using the alias
          await Future.delayed(Duration(milliseconds: delay));
          attempt++;
        } else {
          setState(() {
            _error = 'Failed to load station markers after retries: $e';
            _isLoading = false;
          });
          debugPrint('Error fetching station markers after retries: $e');
        }
      }
    }
  }

  void _startPeriodicRefresh() {
    const Duration refreshInterval = Duration(seconds: 30);
    Timer.periodic(refreshInterval, (timer) async {
      if (mounted) {
        await _fetchData();
      } else {
        timer.cancel();
      }
    });
  }

  void _onStationSelected(Map<String, dynamic> station) {
    setState(() {
      _selectedStation = station;
      _showStationDetails = true;
    });
  }

  void _closeStationDetails() {
    setState(() {
      _showStationDetails = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // DASHBOARD API CONFIGURATION: Clean map for station discovery
          GoogleMapWidget(
            stations: _stationMarkers,
            onTap: (LatLng position) => _closeStationDetails(),
            onStationSelected: _onStationSelected,
            // Set initial camera position to center on India
            initialLatitude: 20.5937,
            initialLongitude: 78.9629,
            initialZoom: 5.0, // Adjust zoom level as needed
            // DASHBOARD: NO polylines, NO route features - clean station browsing
            polylines: const <Polyline>{}, // Explicitly NO polylines for dashboard
            fitBounds: null, // NO route bounds fitting
            onPolylineTapped: null, // NO polyline tap handlers
            additionalMarkers: null, // NO additional route markers
            enableClustering:
                true, // DASHBOARD: Enable clustering for performance
          ),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
          if (_error != null)
            Positioned(
              top: 100,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(204),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _error!,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
          if (_showStationDetails && _selectedStation != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 200,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 10,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _selectedStation!['name'] ?? 'Unknown Station',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: _closeStationDetails,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Status: ${_selectedStation!['status'] ?? 'Unknown'}',
                      style: TextStyle(
                        color: _selectedStation!['status'] == 'Available'
                            ? Colors.green
                            : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('ID: ${_selectedStation!['id'] ?? 'Unknown'}'),
                    const SizedBox(height: 8),
                    Text(
                        'Location: (${_selectedStation!['latitude']}, ${_selectedStation!['longitude']})'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Viewing details for ${_selectedStation!['name']}'),
                          ),
                        );
                      },
                      child: const Text('View Details'),
                    ),
                  ],
                ),
              ),
            ),
          Positioned(
            top: 50,
            right: 16,
            child: FloatingActionButton(
              mini: true,
              onPressed: _fetchData,
              child: const Icon(Icons.refresh),
            ),
          ),
        ],
      ),
    );
  }
}
