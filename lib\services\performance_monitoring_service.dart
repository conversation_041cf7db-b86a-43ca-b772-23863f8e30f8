import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

/// Professional performance monitoring service for Flutter applications
/// Implements real-time frame rate tracking and performance analytics
/// Based on official Flutter performance optimization guidelines
class ApplicationPerformanceMonitor {
  static final ApplicationPerformanceMonitor _instance =
      ApplicationPerformanceMonitor._internal();
  factory ApplicationPerformanceMonitor() => _instance;
  ApplicationPerformanceMonitor._internal();

  // Core performance metrics collection
  final List<Duration> _frameTimeHistory = [];
  final List<double> _frameRateHistory = [];
  int _totalFrameCount = 0;
  DateTime? _previousFrameTimestamp;
  Timer? _metricsReportingTimer;

  // BALANCED PERFORMANCE: High FPS targets with realistic expectations
  static const double optimalFrameRate =
      60.0; // Target 60fps for smooth performance
  static const Duration optimalFrameTime =
      Duration(milliseconds: 16); // 16ms for 60fps
  static const int maxPerformanceHistorySize = 100; // Standard history size
  static const double performanceThresholdFps = 45.0; // Reasonable threshold

  // Monitoring state management
  bool _isActivelyMonitoring = false;
  bool _hasDetectedPerformanceIssues = false;

  /// Initialize performance monitoring system
  void initializeMonitoring() {
    if (_isActivelyMonitoring) return;

    _isActivelyMonitoring = true;
    _startFrameRateTracking();
    _startPerformanceReporting();

    debugPrint(
        'Performance Monitor: Initialized - targeting ${optimalFrameRate}fps');
  }

  /// Start frame rate monitoring using SchedulerBinding
  void _startFrameRateTracking() {
    SchedulerBinding.instance.addPersistentFrameCallback(_onFrameCallback);
  }

  /// Frame callback for measuring frame times
  void _onFrameCallback(Duration timestamp) {
    if (!_isActivelyMonitoring) return;

    final currentTimestamp = DateTime.now();

    if (_previousFrameTimestamp != null) {
      final frameDuration =
          currentTimestamp.difference(_previousFrameTimestamp!);
      _recordFrameMetrics(frameDuration);
    }

    _previousFrameTimestamp = currentTimestamp;
    _totalFrameCount++;
  }

  /// Record frame metrics and calculate performance statistics
  void _recordFrameMetrics(Duration frameTime) {
    _frameTimeHistory.add(frameTime);

    // Calculate frame rate from frame time
    final frameRate = 1000.0 / frameTime.inMilliseconds;
    _frameRateHistory.add(frameRate);

    // Maintain performance history within limits
    if (_frameTimeHistory.length > maxPerformanceHistorySize) {
      _frameTimeHistory.removeAt(0);
      _frameRateHistory.removeAt(0);
    }

    // Analyze performance thresholds
    _analyzePerformanceThresholds(frameTime, frameRate);
  }

  /// Analyze if performance thresholds are exceeded
  void _analyzePerformanceThresholds(Duration frameTime, double frameRate) {
    // Check if frame time exceeds optimal 16ms threshold
    if (frameTime > optimalFrameTime) {
      _hasDetectedPerformanceIssues = true;
      debugPrint(
          'Performance Warning: Frame time exceeded 16ms: ${frameTime.inMilliseconds}ms');
    }

    // Check if frame rate drops below performance threshold
    if (frameRate < performanceThresholdFps) {
      _hasDetectedPerformanceIssues = true;
      debugPrint(
          'Performance Warning: Frame rate dropped below ${performanceThresholdFps}fps: ${frameRate.toStringAsFixed(1)}fps');
    }
  }

  /// Start aggressive performance reporting for ultra-high FPS monitoring
  void _startPerformanceReporting() {
    _metricsReportingTimer =
        Timer.periodic(const Duration(seconds: 2), (timer) {
      // More frequent reporting
      _generatePerformanceReport();
    });
  }

  /// Generate comprehensive performance report
  void _generatePerformanceReport() {
    if (_frameRateHistory.isEmpty) return;

    final averageFrameRate =
        _frameRateHistory.reduce((a, b) => a + b) / _frameRateHistory.length;
    final minimumFrameRate = _frameRateHistory.reduce((a, b) => a < b ? a : b);
    final maximumFrameRate = _frameRateHistory.reduce((a, b) => a > b ? a : b);

    final averageFrameTime =
        _frameTimeHistory.map((d) => d.inMilliseconds).reduce((a, b) => a + b) /
            _frameTimeHistory.length;

    debugPrint('Performance Analytics Report:');
    debugPrint('   Average FPS: ${averageFrameRate.toStringAsFixed(1)}');
    debugPrint('   Minimum FPS: ${minimumFrameRate.toStringAsFixed(1)}');
    debugPrint('   Maximum FPS: ${maximumFrameRate.toStringAsFixed(1)}');
    debugPrint(
        '   Average Frame Time: ${averageFrameTime.toStringAsFixed(1)}ms');
    debugPrint(
        '   Target: ${optimalFrameRate}fps (${optimalFrameTime.inMilliseconds}ms)');

    // Reset performance issue detection after reporting
    if (_hasDetectedPerformanceIssues) {
      debugPrint('Performance Alert: Issues detected in last 5 seconds');
      _hasDetectedPerformanceIssues = false;
    } else {
      debugPrint('Performance Status: Optimal performance maintained');
    }
  }

  /// Get comprehensive performance statistics
  Map<String, dynamic> getPerformanceAnalytics() {
    if (_frameRateHistory.isEmpty) {
      return {
        'averageFrameRate': 0.0,
        'minimumFrameRate': 0.0,
        'maximumFrameRate': 0.0,
        'averageFrameTime': 0.0,
        'totalFrameCount': _totalFrameCount,
        'isPerformanceOptimal': false,
      };
    }

    final averageFrameRate =
        _frameRateHistory.reduce((a, b) => a + b) / _frameRateHistory.length;
    final minimumFrameRate = _frameRateHistory.reduce((a, b) => a < b ? a : b);
    final maximumFrameRate = _frameRateHistory.reduce((a, b) => a > b ? a : b);
    final averageFrameTime =
        _frameTimeHistory.map((d) => d.inMilliseconds).reduce((a, b) => a + b) /
            _frameTimeHistory.length;

    return {
      'averageFrameRate': averageFrameRate,
      'minimumFrameRate': minimumFrameRate,
      'maximumFrameRate': maximumFrameRate,
      'averageFrameTime': averageFrameTime,
      'totalFrameCount': _totalFrameCount,
      'isPerformanceOptimal':
          averageFrameRate >= 55.0 && averageFrameTime <= 18.0,
    };
  }

  /// Check if current performance meets optimal standards
  bool get isPerformanceOptimal {
    if (_frameRateHistory.isEmpty) return false;

    final recentFrameRates = _frameRateHistory.length > 10
        ? _frameRateHistory.sublist(_frameRateHistory.length - 10)
        : _frameRateHistory;

    final averageRecentFrameRate =
        recentFrameRates.reduce((a, b) => a + b) / recentFrameRates.length;
    return averageRecentFrameRate >= 55.0; // Allow 5fps tolerance below 60fps
  }

  /// Mark start of expensive operation for performance tracking
  void startOperationTracking(String operationName) {
    developer.Timeline.startSync(operationName);
  }

  /// Mark end of expensive operation
  void endOperationTracking() {
    developer.Timeline.finishSync();
  }

  /// Track specific operation performance with comprehensive monitoring
  Future<T> trackOperationPerformance<T>(
      String operationName, Future<T> Function() operation) async {
    startOperationTracking(operationName);
    try {
      final result = await operation();
      return result;
    } finally {
      endOperationTracking();
    }
  }

  /// Stop performance monitoring and cleanup resources
  void stopMonitoring() {
    _isActivelyMonitoring = false;
    _metricsReportingTimer?.cancel();
    // Note: Persistent frame callbacks cannot be removed, but we use monitoring flag to disable

    debugPrint('Performance Monitor: Monitoring stopped');
  }

  /// Reset all performance metrics and history
  void resetPerformanceMetrics() {
    _frameTimeHistory.clear();
    _frameRateHistory.clear();
    _totalFrameCount = 0;
    _previousFrameTimestamp = null;
    _hasDetectedPerformanceIssues = false;

    debugPrint('Performance Monitor: Metrics reset');
  }

  /// Legacy method for backward compatibility
  void dispose() => stopMonitoring();

  /// Legacy method for backward compatibility
  void initialize() => initializeMonitoring();

  /// Legacy method for backward compatibility
  Map<String, dynamic> getPerformanceStats() => getPerformanceAnalytics();

  /// Legacy method for backward compatibility
  bool get isPerformant => isPerformanceOptimal;

  /// Legacy method for backward compatibility
  Future<T> trackOperation<T>(
      String operationName, Future<T> Function() operation) async {
    return trackOperationPerformance(operationName, operation);
  }
}
