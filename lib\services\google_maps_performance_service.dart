import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Google Maps performance optimization service based on official Google Maps Platform guidelines
/// Implements marker batching, viewport optimization, and 60fps rendering targets
class GoogleMapsPerformanceService {
  static final GoogleMapsPerformanceService _instance =
      GoogleMapsPerformanceService._internal();
  factory GoogleMapsPerformanceService() => _instance;
  GoogleMapsPerformanceService._internal();

  // BALANCED PERFORMANCE: High FPS with visible markers
  static const int maxMarkersPerBatch =
      25; // Balanced batch size for good performance
  static const int maxVisibleMarkers = 200; // Ensure markers are visible
  static const double viewportPadding =
      0.1; // Standard padding for proper filtering
  static const Duration markerUpdateDebounce =
      Duration(milliseconds: 100); // Fast response
  static const Duration cameraIdleDebounce =
      Duration(milliseconds: 200); // Fast camera response

  // Performance mode configuration
  static const bool enableUltraPerformanceMode =
      false; // Disable ultra mode to show markers
  static const int ultraPerformanceBatchSize = 15; // Reasonable batch size
  static const int ultraPerformanceMaxMarkers =
      150; // Allow more markers to be visible

  // Performance state
  Timer? _markerUpdateTimer;
  Timer? _cameraIdleTimer;
  LatLngBounds? _currentViewport;
  double _currentZoomLevel = 10.0;
  Set<String> _visibleMarkerIds = {};

  // Performance metrics
  int _markerUpdatesCount = 0;
  int _viewportCalculationsCount = 0;
  DateTime? _lastMarkerUpdate;

  /// Calculate optimal viewport bounds with padding for marker visibility
  LatLngBounds calculateOptimalViewport(CameraPosition cameraPosition) {
    _viewportCalculationsCount++;

    // Calculate viewport bounds based on zoom level and screen size
    final double zoomFactor = math.pow(2, 20 - cameraPosition.zoom).toDouble();
    final double latitudeDelta = 180.0 / zoomFactor;
    final double longitudeDelta = 360.0 / zoomFactor;

    // Add padding for smooth marker transitions
    final double paddedLatDelta = latitudeDelta * (1 + viewportPadding);
    final double paddedLngDelta = longitudeDelta * (1 + viewportPadding);

    final LatLng center = cameraPosition.target;

    final LatLngBounds viewport = LatLngBounds(
      southwest: LatLng(
        center.latitude - paddedLatDelta / 2,
        center.longitude - paddedLngDelta / 2,
      ),
      northeast: LatLng(
        center.latitude + paddedLatDelta / 2,
        center.longitude + paddedLngDelta / 2,
      ),
    );

    _currentViewport = viewport;
    _currentZoomLevel = cameraPosition.zoom;

    return viewport;
  }

  /// Filter markers based on viewport visibility for optimal performance
  List<Map<String, dynamic>> filterMarkersInViewport(
    List<Map<String, dynamic>> allMarkers,
    LatLngBounds viewport,
  ) {
    final List<Map<String, dynamic>> visibleMarkers = [];
    final Set<String> newVisibleIds = {};

    for (final marker in allMarkers) {
      final double? lat = marker['latitude'] as double?;
      final double? lng = marker['longitude'] as double?;

      if (lat == null || lng == null) continue;

      // Check if marker is within viewport bounds
      if (lat >= viewport.southwest.latitude &&
          lat <= viewport.northeast.latitude &&
          lng >= viewport.southwest.longitude &&
          lng <= viewport.northeast.longitude) {
        visibleMarkers.add(marker);
        newVisibleIds.add(marker['id']?.toString() ?? '');

        // Limit maximum visible markers for performance
        if (visibleMarkers.length >= maxVisibleMarkers) {
          break;
        }
      }
    }

    _visibleMarkerIds = newVisibleIds;

    debugPrint(
        '🗺️ PERFORMANCE: Filtered ${visibleMarkers.length} visible markers from ${allMarkers.length} total');

    return visibleMarkers;
  }

  /// Batch marker updates for optimal rendering performance
  Future<Set<Marker>> batchMarkerUpdates(
    List<Map<String, dynamic>> markerData,
    Future<Marker?> Function(Map<String, dynamic>) createMarker,
  ) async {
    _markerUpdatesCount++;
    _lastMarkerUpdate = DateTime.now();

    final Set<Marker> markers = {};
    final List<List<Map<String, dynamic>>> batches = [];

    // ULTRA-AGGRESSIVE: Use smaller batch sizes for maximum FPS
    final int batchSize = enableUltraPerformanceMode
        ? ultraPerformanceBatchSize
        : maxMarkersPerBatch;
    final int maxMarkers = enableUltraPerformanceMode
        ? ultraPerformanceMaxMarkers
        : maxVisibleMarkers;

    // Limit total markers for ultra-performance
    final List<Map<String, dynamic>> limitedData =
        markerData.length > maxMarkers
            ? markerData.take(maxMarkers).toList()
            : markerData;

    // Split markers into ultra-small batch sizes for instant processing
    for (int i = 0; i < limitedData.length; i += batchSize) {
      final int end = math.min(i + batchSize, limitedData.length);
      batches.add(limitedData.sublist(i, end));
    }

    debugPrint('🗺️ PERFORMANCE: Processing ${batches.length} marker batches');

    // Process batches with small delays to prevent UI blocking
    for (int batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      final batch = batches[batchIndex];

      // Process batch in parallel for better performance
      final List<Future<Marker?>> markerFutures =
          batch.map(createMarker).toList();
      final List<Marker?> batchMarkers = await Future.wait(markerFutures);

      // Add non-null markers to result set
      for (final marker in batchMarkers) {
        if (marker != null) {
          markers.add(marker);
        }
      }

      // ULTRA-AGGRESSIVE PERFORMANCE: Minimal delay for maximum FPS
      if (batchIndex < batches.length - 1) {
        final int delayMs = enableUltraPerformanceMode
            ? 4
            : 8; // 4ms for 250fps target in ultra mode
        await Future.delayed(Duration(milliseconds: delayMs));
      }
    }

    debugPrint(
        '🗺️ PERFORMANCE: Created ${markers.length} markers from ${markerData.length} data points');

    return markers;
  }

  /// Debounced marker update to prevent excessive rendering
  void scheduleMarkerUpdate(VoidCallback updateCallback) {
    _markerUpdateTimer?.cancel();

    _markerUpdateTimer = Timer(markerUpdateDebounce, () {
      updateCallback();
    });
  }

  /// Debounced camera idle handling for smooth interactions
  void scheduleCameraIdleUpdate(VoidCallback updateCallback) {
    _cameraIdleTimer?.cancel();

    _cameraIdleTimer = Timer(cameraIdleDebounce, () {
      updateCallback();
    });
  }

  /// Calculate optimal marker density based on zoom level
  int calculateOptimalMarkerDensity(double zoomLevel) {
    // Reduce marker density at lower zoom levels for better performance
    if (zoomLevel < 8) {
      return 20; // Very sparse for country/continent view
    } else if (zoomLevel < 12) {
      return 50; // Moderate density for city view
    } else if (zoomLevel < 16) {
      return 100; // Higher density for neighborhood view
    } else {
      return maxVisibleMarkers; // Full density for street view
    }
  }

  /// Check if marker update is necessary based on viewport changes
  bool shouldUpdateMarkers(LatLngBounds newViewport, double newZoomLevel) {
    if (_currentViewport == null) return true;

    // Check if viewport has changed significantly
    final double latDiff =
        (newViewport.northeast.latitude - _currentViewport!.northeast.latitude)
            .abs();
    final double lngDiff = (newViewport.northeast.longitude -
            _currentViewport!.northeast.longitude)
        .abs();
    final double zoomDiff = (newZoomLevel - _currentZoomLevel).abs();

    // Update if viewport moved significantly or zoom changed
    const double significantMovement = 0.001; // ~100 meters
    const double significantZoomChange = 0.5;

    return latDiff > significantMovement ||
        lngDiff > significantMovement ||
        zoomDiff > significantZoomChange;
  }

  /// Optimize camera animations for smooth performance
  CameraUpdate createOptimizedCameraUpdate(LatLng target, double zoom) {
    // Use newCameraPosition for smooth animations
    return CameraUpdate.newCameraPosition(
      CameraPosition(
        target: target,
        zoom: zoom,
        tilt: 0, // Keep tilt at 0 for better performance
        bearing: 0, // Keep bearing at 0 for better performance
      ),
    );
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'markerUpdatesCount': _markerUpdatesCount,
      'viewportCalculationsCount': _viewportCalculationsCount,
      'visibleMarkersCount': _visibleMarkerIds.length,
      'currentZoomLevel': _currentZoomLevel,
      'lastMarkerUpdate': _lastMarkerUpdate?.toIso8601String(),
      'hasActiveViewport': _currentViewport != null,
    };
  }

  /// Reset performance counters
  void resetStats() {
    _markerUpdatesCount = 0;
    _viewportCalculationsCount = 0;
    _lastMarkerUpdate = null;

    debugPrint('🗺️ PERFORMANCE: Stats reset');
  }

  /// Dispose of timers and resources
  void dispose() {
    _markerUpdateTimer?.cancel();
    _cameraIdleTimer?.cancel();

    debugPrint('🗺️ PERFORMANCE: Google Maps performance service disposed');
  }
}
