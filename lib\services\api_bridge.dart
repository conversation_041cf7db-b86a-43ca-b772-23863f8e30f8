import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/station.dart' as app_model;
import '../models/station/station_details_response.dart' as details;
import '../models/station/station_models.dart' hide Connector;
import '../models/station/station_marker_response.dart';
import '../models/station/paginated_stations_response.dart';
// Re-adding necessary imports that were previously removed
import '../models/wallet/wallet_models.dart';
import '../models/user/user_models.dart';
import '../models/nearest_station_response.dart';
import '../models/api_response.dart'; // Import ApiResponse
import '../repositories/station_repository.dart';
import '../repositories/wallet_repository.dart';
import '../repositories/user_repository.dart';

import '../utils/api_exception.dart';
import 'service_locator.dart';
import '../services/auth/auth_service.dart';

/// Helper class to pass parameters to the compute function
class ProcessPaginatedStationsParams {
  final ApiResponse<PaginatedStationsResponse> response;
  final int? Function(String)?
      extractPowerValueFunc; // Made optional - REMOVED power extraction
  final String Function(String) getMapPinUrlFromStatusFunc;

  ProcessPaginatedStationsParams({
    required this.response,
    this.extractPowerValueFunc, // Made optional - we no longer extract power from strings
    required this.getMapPinUrlFromStatusFunc,
  });
}

/// Process paginated stations in a background thread
ApiResponse<List<app_model.Station>> processPaginatedStations(
    ProcessPaginatedStationsParams params) {
  try {
    final response = params.response;
    final getMapPinUrlFromStatus = params.getMapPinUrlFromStatusFunc;

    // Check if data is available
    if (response.data?.data == null) {
      return ApiResponse<List<app_model.Station>>(
        success: false,
        message: 'No data available in response',
        data: [],
      );
    }

    final List<app_model.Station> stations = [];

    debugPrint(
        '🔍 ProcessPaginatedStations: Starting to process ${response.data!.data!.length} stations');
    int processedCount = 0;
    int addedCount = 0;
    int skippedCount = 0;

    for (final paginatedStation in response.data!.data!) {
      processedCount++;
      try {
        // Debug log station data for troubleshooting
        debugPrint(
            '🔍 ProcessPaginatedStations: Processing station ${paginatedStation.name} - UID: "${paginatedStation.uid}", Station ID: ${paginatedStation.stationId}');

        // Debug log distance values from API
        debugPrint(
            '🔍 ProcessPaginatedStations: Station ${paginatedStation.name} - Distance from API: ${paginatedStation.distance}');

        // Create a fallback ID if UID is missing
        final stationUid = paginatedStation.uid?.isNotEmpty == true
            ? paginatedStation.uid!
            : paginatedStation.stationId?.toString() ?? '';

        // Create station object with proper null handling
        final station = app_model.Station(
          id: paginatedStation.stationId?.toString() ?? stationUid,
          uid: stationUid, // Use fallback UID
          name: paginatedStation.name ?? 'Unknown Station',
          address: paginatedStation.address ?? 'No Address',
          latitude: paginatedStation.latitude ?? 0.0,
          longitude: paginatedStation.longitude ?? 0.0,
          distance: paginatedStation.distance ?? 0.0,
          status: paginatedStation.status ?? 'unknown',
          rating: paginatedStation.rating ?? 0.0,
          reviews: paginatedStation.reviewCount ?? 0,
          images: paginatedStation.imageUrl != null &&
                  paginatedStation.imageUrl!.isNotEmpty
              ? [paginatedStation.imageUrl!]
              : [],
          evses: [], // EVSEs not available in paginated response
          connectors: [], // Will be populated from connector types if needed
          types: paginatedStation.types, // Preserve the original types data
          mapPinUrl: getMapPinUrlFromStatus(paginatedStation.status ?? ''),
          focusedMapPinUrl:
              'https://api2.eeil.online/mapicons/ecoplug_focus.png',
        );

        // Add all stations that have either a UID or station ID
        if (stationUid.isNotEmpty) {
          stations.add(station);
          addedCount++;
          debugPrint(
              '✅ ProcessPaginatedStations: Added station ${station.name} with UID: $stationUid');
        } else {
          skippedCount++;
          debugPrint(
              '❌ ProcessPaginatedStations: Skipped station ${paginatedStation.name} - no valid UID or station ID');
        }
      } catch (e) {
        debugPrint(
            '❌ ProcessPaginatedStations: Error processing station ${paginatedStation.name}: $e');
        // Continue with next station
      }
    }

    // Final summary
    debugPrint(
        '🔍 ProcessPaginatedStations: SUMMARY - Processed: $processedCount, Added: $addedCount, Skipped: $skippedCount');
    debugPrint(
        '🔍 ProcessPaginatedStations: Final stations list size: ${stations.length}');

    return ApiResponse<List<app_model.Station>>(
      success: true,
      message: response.message,
      data: stations,
    );
  } catch (e) {
    return ApiResponse<List<app_model.Station>>(
      success: false,
      message: 'Error processing paginated stations: $e',
      data: [],
    );
  }
}

/// Bridge between existing app code and new API structure
/// This class ensures that the existing UI/UX can work with the new API structure
/// without requiring changes to the UI code
class ApiBridge {
  // Singleton pattern
  static final ApiBridge _instance = ApiBridge._internal();
  factory ApiBridge() => _instance;
  ApiBridge._internal();

  /// Helper method to get map pin URL based on station status
  String _getMapPinUrlFromStatus(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return 'https://api2.eeil.online/mapicons/ecoplug_available.png';
      case 'in use':
      case 'inuse':
      case 'charging':
        return 'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png';
      case 'unavailable':
      case 'offline':
        return 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png';
      default:
        return 'https://api2.eeil.online/mapicons/ecoplug_default.png';
    }
  }

  String _getMapPinUrlFromStatusStrict(String status) {
    final lowerStatus = status.toLowerCase();
    switch (lowerStatus) {
      case 'available':
        return 'https://api2.eeil.online/mapicons/ecoplug_available.png';
      case 'in use':
      case 'inuse':
      case 'charging':
        return 'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png';
      case 'unavailable':
      case 'offline':
        return 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png';
      default:
        // Return a default value instead of throwing an exception
        return 'https://api2.eeil.online/mapicons/ecoplug_default.png';
    }
  }

  // Repositories
  final StationRepository _stationRepository =
      ServiceLocator().stationRepository;
  final WalletRepository _walletRepository = ServiceLocator().walletRepository;
  final UserRepository _userRepository = ServiceLocator().userRepository;
  final AuthService _authService = AuthService();

  // Station methods

  /// Convert StationDetail to Station (old model)
  app_model.Station _convertToOldStationModel(StationDetail stationDetail) {
    // Create a list of connectors in the old format directly
    final List<app_model.Connector> stationConnectors =
        stationDetail.connectors.map((newConnector) {
      // newConnector is an instance of Connector from lib/models/station/station_models.dart

      // Ensure valid connector UID and type; no fallback defaults allowed
      if (newConnector.evsesUid == null ||
          newConnector.evsesUid!.trim().isEmpty) {
        throw FormatException('Connector real-time UID is missing');
      }
      if (newConnector.type == null || newConnector.type!.trim().isEmpty) {
        throw FormatException('Connector type (name) is missing');
      }

      // totalGuns is not available in the new API's Connector model, set to null
      final int? totalGuns = null;

      // Derive availableGuns from status
      final int availableGuns = ['available', 'active', 'ready']
              .contains(newConnector.status?.toLowerCase())
          ? 1
          : 0; // As per summary: deriving availableGuns

      // CRITICAL: Do NOT compute maxElectricPower from powerOutput
      // Only use direct maxElectricPower values from API responses
      // This ensures we only display real API data, not computed values
      dynamic maxElectricPower; // PRESERVE EXACT API FORMAT

      // Check if the connector has a maxPower field (from the station models)
      if (newConnector.maxPower != null &&
          (newConnector.maxPower is num
              ? newConnector.maxPower! > 0
              : newConnector.maxPower.toString().isNotEmpty)) {
        maxElectricPower = newConnector.maxPower; // PRESERVE EXACT API FORMAT
        debugPrint(
            'Using direct maxPower from API preserving exact format: $maxElectricPower (${maxElectricPower.runtimeType})');
      } else {
        debugPrint(
            'No direct maxPower from API - not computing from powerOutput');
        maxElectricPower = null; // Keep as null - do not compute
      }

      // Handle price conversion
      double? price;
      if (newConnector.pricePerKwh != null) {
        if (newConnector.pricePerKwh is num) {
          price = (newConnector.pricePerKwh as num).toDouble();
        } else if (newConnector.pricePerKwh is String) {
          // Handle currency symbols and formatting
          final numericString = (newConnector.pricePerKwh as String)
              .replaceAll(RegExp(r'[^0-9.]'), '');
          price = double.tryParse(numericString); // Keep null if parsing fails
        }
      } // If newConnector.pricePerKwh is null, price remains null

      return app_model.Connector(
        id: newConnector.evsesUid!,
        name: newConnector.type!,
        type: newConnector.type!,
        price: price,
        power: newConnector.powerOutput ??
            (throw FormatException('Connector powerOutput is missing')),
        totalGuns: totalGuns,
        availableGuns: availableGuns,
        icon: newConnector.imageUrl ??
            (throw FormatException('Connector imageUrl is missing')),
        maxElectricPower: maxElectricPower,
        status: newConnector.status ??
            (throw FormatException('Connector status is missing')),
      );
    }).toList();

    // Validate and prepare data for app_model.Station
    final String stationId =
        stationDetail.id != null && stationDetail.id!.isNotEmpty
            ? stationDetail.id!
            : (throw FormatException('Station ID from API is null or empty'));
    final String stationName =
        stationDetail.name != null && stationDetail.name!.isNotEmpty
            ? stationDetail.name!
            : (throw FormatException('Station Name from API is null or empty'));
    final String stationAddress = stationDetail.address != null &&
            stationDetail.address!.isNotEmpty
        ? stationDetail.address!
        : (throw FormatException('Station Address from API is null or empty'));
    final double stationLatitude = stationDetail.latitude ??
        (throw FormatException('Station Latitude from API is null'));
    final double stationLongitude = stationDetail.longitude ??
        (throw FormatException('Station Longitude from API is null'));
    final double stationRating = stationDetail.rating ??
        (throw FormatException('Station Rating from API is null'));
    final int stationReviewCount = stationDetail.reviewCount ??
        (throw FormatException('Station Review Count from API is null'));
    final String stationStatus = stationDetail.status != null &&
            stationDetail.status!.isNotEmpty
        ? stationDetail.status!
        : (throw FormatException('Station Status from API is null or empty'));

    final List<String> stationImages =
        stationDetail.imageUrl != null && stationDetail.imageUrl!.isNotEmpty
            ? [stationDetail.imageUrl!]
            : [];

    final String? stationCity = stationAddress.split(',').length > 1
        ? stationAddress.split(',').last.trim()
        : null;

    final String? stationOpeningTimes =
        stationDetail.operatingHours; // Map operatingHours from new model

    final String mapPin =
        _getMapPinUrlFromStatusStrict(stationStatus); // Use validated status

    return app_model.Station(
      id: stationId,
      uid: stationDetail.uid ?? '', // Handle nullable UID safely
      name: stationName,
      address: stationAddress,
      latitude: stationLatitude,
      longitude: stationLongitude,
      distance: 0.0, // Placeholder; to be calculated or from API if available
      rating: stationRating,
      reviews: stationReviewCount,
      status: stationStatus,
      connectors: stationConnectors,
      images: stationImages,
      evses: [], // Placeholder, old model structure not directly available from new API
      city: stationCity,
      state: null, // Not available in StationDetail
      openingTimes: stationOpeningTimes, // Mapped from operatingHours
      mapPinUrl: mapPin,
      focusedMapPinUrl:
          'https://api2.eeil.online/mapicons/ecoplug_focus.png', // Default focus pin
    );
  }

  /// Get all stations
  Future<List<app_model.Station>> getAllStations() async {
    final response = await _stationRepository.getPaginatedStations(1, 100);

    if (response.success &&
        response.data != null &&
        response.data!.data != null) {
      try {
        // Convert PaginatedStation objects to Station objects
        return response.data!.data!.map((paginatedStation) {
          // Create a Station object from PaginatedStation
          List<app_model.Connector> connectors = [];

          try {
            // Handle potential type mismatch in connector types
            connectors =
                paginatedStation.getConnectorTypes().map((connectorType) {
              return app_model.Connector(
                id: 'conn-${connectorType.name}',
                name: connectorType.name ?? 'Unknown',
                type: connectorType.name ?? 'Unknown',
                price: 0.0, // Price not provided in paginated response
                power: connectorType.power ??
                    '', // Use power from API data instead of empty default
                totalGuns:
                    connectorType.guns ?? 1, // Use guns from API if available
                availableGuns: connectorType.availableGuns ??
                    1, // Use available guns from API if available
              );
            }).toList();
          } catch (e) {
            debugPrint('Error converting connector types: $e');
            // Return empty connectors list on error
          }

          return app_model.Station(
            id: paginatedStation.stationId?.toString() ?? '',
            uid: paginatedStation.uid ?? '',
            name: paginatedStation.name ?? 'Unknown Station',
            address: paginatedStation.address ?? 'No Address',
            latitude: paginatedStation.latitude ?? 0.0,
            longitude: paginatedStation.longitude ?? 0.0,
            distance:
                paginatedStation.distance ?? 0.0, // Use distance if available
            status: paginatedStation.status ?? 'Unknown',
            rating: paginatedStation.rating ?? 0.0, // Use rating if available
            reviews: paginatedStation.reviewCount ??
                0, // Use reviewCount if available
            connectors: connectors,
            images: paginatedStation.imageUrl != null &&
                    paginatedStation.imageUrl!.isNotEmpty
                ? [paginatedStation.imageUrl!]
                : ['https://api2.eeil.online/uploads/ev-banner2.png'],
            evses: [],
          );
        }).toList();
      } catch (e) {
        debugPrint('Error in getAllStations: $e');
        return []; // Return empty list on error
      }
    }

    return [];
  }

  /// Get station by ID
  Future<app_model.Station?> getStationById(String stationId) async {
    final response = await _stationRepository.getStationDetail(stationId);

    if (response.success && response.data != null) {
      return _convertToOldStationModel(response.data!);
    }

    return null;
  }

  /// Get station details by UID using the new API endpoint
  Future<details.StationDetailsResponse?> getStationDetailsByUid(
      String uid) async {
    // Input validation - check for empty UID
    if (uid.trim().isEmpty) {
      throw FormatException('INVALID_UID: Station UID cannot be empty');
    }

    // Add retry mechanism for reliability
    const int maxRetries = 3;
    int attempts = 0;
    Exception? lastError;

    while (attempts < maxRetries) {
      attempts++;

      try {
        // Make a direct HTTP request to ensure we're using the exact endpoint
        final fullEndpoint =
            'https://api2.eeil.online/api/v1/user/stations/details?uid=$uid';

        final http.Client client = http.Client();

        try {
          // Get the auth token using SharedPreferences directly for reliability
          final prefs = await SharedPreferences.getInstance();
          final token = prefs.getString('auth_token');

          if (token == null || token.isEmpty) {
            debugPrint('ApiBridge: No auth token available');
            throw Exception('Authentication token is missing');
          }

          // Create headers with auth token
          final headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer $token',
          };

          debugPrint('ApiBridge: Making direct HTTP request with auth token');
          final response = await client
              .get(
                Uri.parse(fullEndpoint),
                headers: headers,
              )
              .timeout(const Duration(
                  seconds: 15)); // Reduced timeout for faster response

          debugPrint('ApiBridge: Response status code: ${response.statusCode}');

          if (response.statusCode == 200) {
            final jsonData = jsonDecode(response.body);
            debugPrint('ApiBridge: Response received successfully');

            // Parse the response using the StationDetailsResponse model
            final stationDetailsResponse =
                details.StationDetailsResponse.fromJson(jsonData);

            // Log the parsed response
            debugPrint(
                'ApiBridge: Parsed response success: ${stationDetailsResponse.success}');
            if (stationDetailsResponse.data != null) {
              final stationData = stationDetailsResponse.data!;

              debugPrint('''
                ApiBridge: Station data details:
                - Name: ${stationData.name}
                - Address: ${stationData.address}
                - Latitude: ${stationData.latitude}
                - Longitude: ${stationData.longitude}
                - EVSEs count: ${stationData.evses?.length ?? 0}
                - UID from API: ${stationData.uid}
              ''');

              // BULLETPROOF UID PRESERVATION - Ensure UID is available from request parameter
              debugPrint(
                  'ApiBridge: Preserving UID from request parameter: $uid');
              stationData.preserveUidFromRequest(uid);
              debugPrint(
                  'ApiBridge: Station UID after preservation: ${stationData.uid}');

              // Validate required fields
              final validationErrors = <String>[];

              // Check name
              if (stationData.name == null ||
                  stationData.name!.trim().isEmpty) {
                validationErrors.add('Station name is missing');
              }

              // Check address
              if (stationData.address == null ||
                  stationData.address!.trim().isEmpty) {
                validationErrors.add('Station address is missing');
              }

              // Check coordinates
              if (stationData.latitude == null ||
                  stationData.longitude == null) {
                validationErrors.add('Station coordinates are missing');
              }

              // Check EVSEs
              if (stationData.evses == null || stationData.evses!.isEmpty) {
                validationErrors.add('No EVSEs found for this station');
              }

              // If we have validation errors, log them as warnings
              if (validationErrors.isNotEmpty) {
                debugPrint('ApiBridge: Validation warnings for station data:');
                for (final error in validationErrors) {
                  debugPrint('  - $error');
                }

                // Determine if we have the minimum required data
                final hasValidName =
                    stationData.name?.trim().isNotEmpty ?? false;
                final hasValidCoords = stationData.latitude != null &&
                    stationData.longitude != null;

                // If we're missing critical data, consider it a failure
                if (!hasValidName || !hasValidCoords) {
                  final missingFields = <String>[];
                  if (!hasValidName) missingFields.add('name');
                  if (!hasValidCoords) missingFields.add('coordinates');

                  if (attempts < maxRetries) {
                    await Future.delayed(Duration(seconds: 1 * attempts));
                    continue;
                  } else {
                    throw Exception(
                        'Failed to load complete station data after $maxRetries attempts. Missing: ${missingFields.join(', ')}');
                  }
                }
              }
            } else {
              throw Exception('Station data is null in API response');
            }

            return stationDetailsResponse;
          } else {
            debugPrint('ApiBridge: Error response: ${response.body}');
            throw Exception(
                'Failed to fetch station details: HTTP ${response.statusCode}');
          }
        } finally {
          client.close();
        }
      } on TimeoutException catch (e) {
        lastError = e;

        if (attempts >= maxRetries) break;
        await Future.delayed(Duration(seconds: 1 * attempts));
      } on SocketException catch (e) {
        lastError = e;

        if (attempts >= maxRetries) break;
        await Future.delayed(Duration(seconds: 2 * attempts));
      } on FormatException catch (e) {
        lastError = e;

        if (attempts >= maxRetries) break;
        await Future.delayed(Duration(seconds: 1));
      } on Exception catch (e) {
        lastError = e;

        if (attempts >= maxRetries) break;
        await Future.delayed(Duration(seconds: 1 * attempts));
      }
    }

    // If we get here, all retries failed
    debugPrint(
        'ApiBridge: Failed to fetch station details after $maxRetries attempts');
    throw lastError ??
        Exception('Failed to fetch station details after $maxRetries attempts');
  }

  /// Get paginated stations with optional filtering
  Future<ApiResponse<List<app_model.Station>>> getPaginatedStations(
      int page, int limit,
      {double? latitude,
      double? longitude,
      String? powerOutput,
      List<String>? connectorStandards}) async {
    try {
      debugPrint(
          'ApiBridge: Getting paginated stations for page $page with limit $limit');
      if (latitude != null && longitude != null) {
        debugPrint('ApiBridge: Including user location: $latitude, $longitude');
      }
      if (powerOutput != null) {
        debugPrint('ApiBridge: Including power output filter: $powerOutput');
      }
      if (connectorStandards != null && connectorStandards.isNotEmpty) {
        debugPrint(
            'ApiBridge: Including connector standards filter: $connectorStandards');
      }

      // Call the repository to get paginated stations with filters
      final response = await _stationRepository.getPaginatedStations(
          page, limit,
          latitude: latitude,
          longitude: longitude,
          powerOutput: powerOutput,
          connectorStandards: connectorStandards);

      if (response.success && response.data != null) {
        // Use compute to move heavy processing to a background thread
        return await compute(
            processPaginatedStations,
            ProcessPaginatedStationsParams(
              response: response,
              extractPowerValueFunc:
                  null, // REMOVED: No power extraction - only direct API values
              getMapPinUrlFromStatusFunc: _getMapPinUrlFromStatus,
            ));
      } else {
        debugPrint(
            'ApiBridge: Failed to get paginated stations: ${response.message}');
        return ApiResponse<List<app_model.Station>>(
          success: false,
          message: response.message,
          data: [],
        );
      }
    } catch (e) {
      debugPrint('ApiBridge: Error in getPaginatedStations: $e');
      return ApiResponse<List<app_model.Station>>(
        success: false,
        message: 'Error fetching paginated stations: $e',
        data: [],
      );
    }
  }

  /// REMOVED: Power extraction method - we only use direct API values
  /// This method was used to compute power from strings, which violates our
  /// zero-tolerance policy for computed power values. All power data must
  /// come directly from API responses without any parsing or computation.

  /// Get nearby stations
  Future<List<app_model.Station>> getNearbyStations(
      double latitude, double longitude,
      {double radius = 10.0}) async {
    final response = await _stationRepository
        .getNearestStations(latitude, longitude, radius: radius);

    if (response.success && response.data != null) {
      return response.data!
          .map((nearestStation) => _convertFromNearestStation(nearestStation))
          .toList();
    }

    return [];
  }

  /// Convert a NearestStation to a Station object
  app_model.Station _convertFromNearestStation(NearestStation nearestStation) {
    // Extract connector types from the types field
    List<app_model.Connector> connectors = [];

    // Get connector types using the helper method
    final connectorTypes = nearestStation.getConnectorTypes();

    // Create a connector for each type
    for (var type in connectorTypes) {
      // Extract additional data from the raw types data if available
      String power = '';
      double price = 0.0;
      int totalGuns = 1;
      int availableGuns = nearestStation.status == 'Available' ? 1 : 0;
      dynamic maxPower; // PRESERVE EXACT API FORMAT

      // Try to extract data from the raw types data
      if (nearestStation.types is Map) {
        final typesMap = nearestStation.types as Map;
        typesMap.forEach((key, value) {
          if (value is Map && value['name'] == type.name) {
            // Extract power ONLY from direct API data - no parsing from names
            if (value.containsKey('power')) {
              power = value['power']?.toString() ?? '';
              debugPrint('🔍 Using direct power value from API: $power');
            } else {
              debugPrint(
                  '❌ No power value available from API for connector type: ${type.name}');
            }

            // Only use maxElectricPower if directly provided by API - PRESERVE EXACT FORMAT
            if (value.containsKey('maxElectricPower') &&
                value['maxElectricPower'] != null &&
                (value['maxElectricPower'] is num
                    ? value['maxElectricPower'] > 0
                    : value['maxElectricPower'].toString().isNotEmpty)) {
              maxPower = value['maxElectricPower']; // PRESERVE EXACT API FORMAT
              debugPrint(
                  '✅ Using direct maxElectricPower from API preserving exact format: $maxPower (${maxPower.runtimeType})');
            } else {
              debugPrint(
                  '❌ No maxElectricPower available from API for connector type: ${type.name}');
              maxPower = null; // Keep null when no API data available
            }

            // Extract price if available
            if (value.containsKey('price')) {
              if (value['price'] is num) {
                price = (value['price'] as num).toDouble();
              } else if (value['price'] is String) {
                price = double.tryParse(value['price']) ?? 0.0;
              }
            }

            // Extract guns data if available
            if (value.containsKey('total_guns')) {
              totalGuns = value['total_guns'] is num
                  ? (value['total_guns'] as num).toInt()
                  : 1;
            }

            if (value.containsKey('available_guns')) {
              availableGuns = value['available_guns'] is num
                  ? (value['available_guns'] as num).toInt()
                  : (nearestStation.status == 'Available' ? 1 : 0);
            }
          }
        });
      } else if (nearestStation.types is List) {
        final typesList = nearestStation.types as List;
        for (var item in typesList) {
          if (item is Map && item['name'] == type.name) {
            // Extract power ONLY from direct API data - no parsing from names
            if (item.containsKey('power')) {
              power = item['power']?.toString() ?? '';
              debugPrint('🔍 Using direct power value from API: $power');
            } else {
              debugPrint(
                  '❌ No power value available from API for connector type: ${type.name}');
            }

            // Only use maxElectricPower if directly provided by API - PRESERVE EXACT FORMAT
            if (item.containsKey('maxElectricPower') &&
                item['maxElectricPower'] != null &&
                (item['maxElectricPower'] is num
                    ? item['maxElectricPower'] > 0
                    : item['maxElectricPower'].toString().isNotEmpty)) {
              maxPower = item['maxElectricPower']; // PRESERVE EXACT API FORMAT
              debugPrint(
                  '✅ Using direct maxElectricPower from API preserving exact format: $maxPower (${maxPower.runtimeType})');
            } else {
              debugPrint(
                  '❌ No maxElectricPower available from API for connector type: ${type.name}');
              maxPower = null; // Keep null when no API data available
            }

            // Extract price if available
            if (item.containsKey('price')) {
              if (item['price'] is num) {
                price = (item['price'] as num).toDouble();
              } else if (item['price'] is String) {
                price = double.tryParse(item['price']) ?? 0.0;
              }
            }

            // Extract guns data if available
            if (item.containsKey('total_guns')) {
              totalGuns = item['total_guns'] is num
                  ? (item['total_guns'] as num).toInt()
                  : 1;
            }

            if (item.containsKey('available_guns')) {
              availableGuns = item['available_guns'] is num
                  ? (item['available_guns'] as num).toInt()
                  : (nearestStation.status == 'Available' ? 1 : 0);
            }
          }
        }
      }

      connectors.add(
        app_model.Connector(
          id: '${nearestStation.stationId}_${type.name}',
          name: type.name ?? 'Unknown',
          type: type.name ?? 'Unknown',
          price: price,
          power: power,
          totalGuns: totalGuns,
          availableGuns: availableGuns,
          status: nearestStation.status ?? 'Unknown',
          maxElectricPower: maxPower,
          icon: type.icon,
        ),
      );
    }

    // CRITICAL: No default connectors - if no connectors found, skip this station
    // This ensures we only show stations with real connector data
    if (connectors.isEmpty) {
      debugPrint(
          'ApiBridge: Skipping station ${nearestStation.stationId} - no valid connectors found');
      throw FormatException(
          'No valid connectors found for station ${nearestStation.stationId}');
    }

    // Try to extract image URL from the raw data - no fallback placeholder
    String? imageUrl;
    if (nearestStation.types is Map) {
      final typesMap = nearestStation.types as Map;
      typesMap.forEach((key, value) {
        if (value is Map && value.containsKey('station_image')) {
          final stationImage = value['station_image'];
          if (stationImage != null && stationImage.toString().isNotEmpty) {
            imageUrl = stationImage.toString();
          }
        }
      });
    } else if (nearestStation.types is List) {
      final typesList = nearestStation.types as List;
      for (var item in typesList) {
        if (item is Map && item.containsKey('station_image')) {
          final stationImage = item['station_image'];
          if (stationImage != null && stationImage.toString().isNotEmpty) {
            imageUrl = stationImage.toString();
          }
        }
      }
    }

    // Use real API image or default API image - no placeholder
    final finalImageUrl =
        imageUrl ?? 'https://api2.eeil.online/uploads/ev-banner2.png';

    // Try to extract rating and reviews from the raw data
    double rating = 0.0;
    int reviews = 0;
    if (nearestStation.types is Map) {
      final typesMap = nearestStation.types as Map;
      typesMap.forEach((key, value) {
        if (value is Map) {
          if (value.containsKey('rating')) {
            if (value['rating'] is num) {
              rating = (value['rating'] as num).toDouble();
            } else if (value['rating'] is String) {
              rating = double.tryParse(value['rating']) ?? 0.0;
            }
          }
          if (value.containsKey('reviews')) {
            if (value['reviews'] is num) {
              reviews = (value['reviews'] as num).toInt();
            } else if (value['reviews'] is String) {
              reviews = int.tryParse(value['reviews']) ?? 0;
            }
          }
        }
      });
    }

    // Log the UID for debugging
    debugPrint(
        'ApiBridge: Converting NearestStation with UID: ${nearestStation.uid}, station_id: ${nearestStation.stationId}');

    // Accept any UID from the API
    debugPrint(
        'ApiBridge: Using UID from NearestStation: ${nearestStation.uid}');

    return app_model.Station(
      // Use the station_id for the id field (for display purposes)
      id: nearestStation.stationId?.toString() ?? '',
      name: nearestStation.name ?? '',
      address: _formatAddress(nearestStation.address, nearestStation.city),
      city: nearestStation.city,
      state: null, // Not available in NearestStation
      latitude: nearestStation.latitude ?? 0.0,
      longitude: nearestStation.longitude ?? 0.0,
      distance: nearestStation.distance ?? 0.0,
      status: nearestStation.status ?? '',
      rating: rating,
      reviews: reviews,
      connectors: connectors,
      images: [finalImageUrl],
      evses: [],
      mapPinUrl: _getMapPinUrlFromStatus(nearestStation.status ?? ''),
      focusedMapPinUrl: 'https://api2.eeil.online/mapicons/ecoplug_focus.png',
      // Use the uid field for API calls - this is the UUID from the API
      uid: nearestStation.uid,
    );
  }

  /// Format address by combining address and city
  String _formatAddress(String? address, String? city) {
    if (address == null || address.isEmpty) {
      return city ?? 'No Address';
    }
    if (city == null || city.isEmpty) {
      return address;
    }
    // Check if the address already contains the city name
    if (address.contains(city)) {
      return address;
    }
    return '$address, $city';
  }

  /// Search stations
  Future<List<app_model.Station>> searchStations(String query) async {
    final response = await _stationRepository.searchStations(query);

    if (response.success && response.data != null) {
      return response.data!
          .map((nearestStation) => _convertFromNearestStation(nearestStation))
          .toList();
    }

    return [];
  }

  /// Get station markers
  Future<List<StationMarker>> getStationMarkers() async {
    final response = await _stationRepository.getMarkers();

    if (response.success && response.data != null) {
      return response.data!;
    }

    return [];
  }

  /// Get station markers from the new API endpoint with error handling
  Future<List<StationMarkerData>> getApiStationMarkers() async {
    debugPrint('\n=== API BRIDGE: GETTING STATION MARKERS ===');
    try {
      debugPrint('Calling station repository getStationMarkers');
      final response = await _stationRepository.getStationMarkers();

      debugPrint(
          'Response received: success=${response.success}, has data=${response.data != null}, message=${response.message}');

      if (response.success && response.data != null) {
        debugPrint('Successfully got ${response.data!.length} station markers');
        return response.data!;
      } else {
        // If the response is not successful but has a message, throw an exception
        // This will allow the UI to display the error message
        if (response.message.isNotEmpty) {
          debugPrint('Throwing exception with message: ${response.message}');
          throw Exception(response.message);
        } else {
          debugPrint('Response unsuccessful but no message provided');
          throw Exception('Failed to load station data');
        }
      }
    } catch (e) {
      debugPrint('Error in getApiStationMarkers: $e');
      // Check if this is a cached data response
      if (e.toString().contains('Using cached station data')) {
        debugPrint('This is a cached data response, handling specially');
        // This is actually a success case with cached data
        // Try to get cached data directly from the repository
        try {
          // Create a new instance of StationDataCache to access cached data directly
          final stationDataCache = ServiceLocator().stationDataCache;
          final cachedMarkers = await stationDataCache.getCachedStationMarkers(
              ignoreExpiration: true);

          if (cachedMarkers != null && cachedMarkers.isNotEmpty) {
            debugPrint(
                'Successfully retrieved ${cachedMarkers.length} cached markers directly');
            return cachedMarkers;
          } else {
            debugPrint('No cached markers found');
          }
        } catch (cacheError) {
          debugPrint('Error accessing cached data directly: $cacheError');
        }
      }

      throw ApiException(
        'Failed to load station markers. Please check your internet connection and try again.',
        code: 'LOAD_STATION_MARKERS_ERROR',
      );
    }
  }

  /// Convert StationMarkerData to a format compatible with the map
  Map<String, dynamic> convertMarkerToMapFormat(StationMarkerData marker) {
    // Determine status based on the marker URL
    String status = marker.status;
    if (status.isEmpty && marker.mapPinUrl != null) {
      if (marker.mapPinUrl!.contains('unavailable')) {
        status = 'Unavailable';
      } else if (marker.mapPinUrl!.contains('charging')) {
        status = 'In Use';
      } else {
        status = 'Available';
      }
    }

    // Create a map with station data for the UI
    return {
      'id': marker.id.toString(),
      'name': marker.name,
      'latitude': marker.latitude,
      'longitude': marker.longitude,
      'address': marker.address ?? '',
      'status': status,
      'availability': status,
      'connectorType': marker.connectorType ?? 'Various',
      'freeGuns': status == 'Available' ? 1 : 0,
      'mapPinUrl': marker.mapPinUrl ?? _getMapPinUrlFromStatus(status),
      'focusedMapPinUrl': marker.focusedMapPinUrl ??
          'https://api2.eeil.online/mapicons/ecoplug_focus.png',
    };
  }

  // Wallet methods

  /// Get wallet info
  Future<WalletInfo?> getWalletInfo() async {
    final response = await _walletRepository.getWalletInfo();

    if (response.success && response.data != null) {
      return response.data;
    }

    return null;
  }

  /// Start charging transaction
  Future<String?> startTransaction(String stationId, String connectorId,
      {String? vehicleId, String? promocodeId}) async {
    final response = await _walletRepository.startTransaction(
        stationId, connectorId, vehicleId, promocodeId);

    if (response.success && response.data != null) {
      return response.data;
    }

    return null;
  }

  /// Stop charging transaction
  Future<BillingDetails?> stopTransaction(String transactionId) async {
    final response = await _walletRepository.stopTransaction(transactionId);

    if (response.success && response.data != null) {
      return response.data;
    }

    return null;
  }

  /// Get ongoing charging sessions
  Future<List<ChargingSession>> getOngoingSessions() async {
    final response = await _walletRepository.getOngoingSessions();

    if (response.success && response.data != null) {
      return response.data!;
    }

    return [];
  }

  /// Get charging session history
  Future<List<ChargingSession>> getChargingSessionHistory() async {
    final response = await _walletRepository.getChargingSessionHistory();

    if (response.success && response.data != null) {
      return response.data!;
    }

    return [];
  }

  // User methods

  /// Get user profile
  Future<UserProfile?> getUserProfile() async {
    final response = await _userRepository.getUserProfile();

    if (response.success && response.data != null) {
      return response.data;
    }

    return null;
  }

  /// Update user profile
  Future<bool?> updateUserProfile(
      {required String userId,
      required String name,
      required String email,
      String? gstNumber,
      String? businessName}) async {
    try {
      final response = await _userRepository.updateUserProfileWithGst(
          userId: userId,
          name: name,
          email: email,
          gstNumber: gstNumber,
          businessName: businessName);

      if (response.success) {
        return response.data ?? true;
      }
      // Return null if the update failed
      return null;
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return null;
    }
  }

  /// Get user vehicles
  Future<List<Vehicle>> getUserVehicles() async {
    final response = await _userRepository.getVehicles();

    if (response.success && response.data != null) {
      return response.data!;
    }

    return [];
  }

  /// Save vehicle
  Future<Vehicle?> saveVehicle({
    String? id,
    required String name,
    required String licensePlate,
    String? brand,
    String? model,
    String? year,
  }) async {
    final response = await _userRepository.saveVehicle(
      id: id,
      name: name,
      licensePlate: licensePlate,
      brand: brand,
      model: model,
      year: year,
    );

    if (response.success && response.data != null) {
      return response.data;
    }

    return null;
  }

  /// Set default vehicle
  Future<bool> setDefaultVehicle(String vehicleId) async {
    final response = await _userRepository.setDefaultVehicle(vehicleId);

    return response.success && response.data != null && response.data!;
  }

  /// Get promocodes
  Future<List<Promocode>> getPromocodes() async {
    final response = await _userRepository.getPromocodes();

    if (response.success && response.data != null) {
      return response.data!;
    }

    return [];
  }

  /// Verify promocode
  Future<Promocode?> verifyPromocode(String code) async {
    final response = await _userRepository.verifyPromocode(code);

    if (response.success && response.data != null) {
      return response.data;
    }

    return null;
  }

  // Auth methods

  /// Login with phone number
  Future<String?> login(String phoneNumber) async {
    final response = await _authService.sendOtp(phoneNumber);

    if (response['success'] == true) {
      return phoneNumber; // Return phone number as request ID
    }

    return null;
  }

  /// Verify OTP
  Future<bool> verifyOtp(String requestId, String otp) async {
    final response = await _authService.verifyOtp(requestId, otp);

    return response['success'] == true;
  }

  /// Logout
  Future<bool> logout() async {
    try {
      await _authService.logout();
      return true;
    } catch (e) {
      debugPrint('Logout error: $e');
      return false;
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _authService.isLoggedIn();
  }

  /// Get reviews for a station
  Future<Map<String, dynamic>?> getReviews(String locationId) async {
    try {
      debugPrint('ApiBridge: Fetching reviews for location ID: $locationId');

      // Make a direct HTTP request to the reviews API
      final url =
          'https://api2.eeil.online/api/v1/user/reviews?location_id=$locationId';

      // Get the auth token
      final token = await _authService.getToken();

      if (token == null || token.isEmpty) {
        debugPrint(
            'ApiBridge: No auth token available for reviews API request');
        return {
          'success': false,
          'message': 'Authentication required',
          'reviews': []
        };
      }

      // Create headers with auth token
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      // Make the request
      final response = await http.get(Uri.parse(url), headers: headers);

      debugPrint(
          'ApiBridge: Reviews API Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        debugPrint(
            'ApiBridge: Successfully fetched reviews: ${jsonData['reviews']?.length ?? 0} reviews');
        return jsonData;
      } else {
        debugPrint('ApiBridge: Error fetching reviews: ${response.body}');
        return {
          'success': false,
          'message': 'Failed to load reviews: ${response.statusCode}',
          'reviews': []
        };
      }
    } catch (e) {
      debugPrint('ApiBridge: Exception in getReviews: $e');
      return {'success': false, 'message': 'Error: $e', 'reviews': []};
    }
  }
}
