import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../utils/app_themes.dart';
import 'package:cached_network_image/cached_network_image.dart';

class FilterDialog extends StatefulWidget {
  // Enhanced connector type data structure with standard codes and icon URLs
  static const Map<String, Map<String, String>> connectorTypeData = {
    'CCS2': {
      'standard': 'IEC_62196_T2_COMBO',
      'iconUrl': 'https://api2.eeil.online/uploads/connector_type/ccs2.svg',
    },
    'TYPE 2': {
      'standard': 'IEC_62196_T2',
      'iconUrl': 'https://api2.eeil.online/uploads/connector_type/type2.svg',
    },
    'SMARTPLUG': {
      'standard': 'SMART_PLUG',
      'iconUrl': 'https://api2.eeil.online/uploads/connector_type/splug1.svg',
    },
    '3 Pin': {
      'standard': 'IEC_60309_2_three_16',
      'iconUrl': 'https://api2.eeil.online/uploads/connector_type/3pin16ac.svg',
    },
    'GB/T': {
      'standard': 'GBT_DC',
      'iconUrl': 'https://api2.eeil.online/uploads/connector_type/gbt_fast.svg',
    },
    'CHAdeMO': {
      'standard': 'CHADEMO',
      'iconUrl': 'https://api2.eeil.online/uploads/connector_type/chademo.svg',
    },
    'Wall': {
      'standard': 'IEC_62196_T3A',
      'iconUrl': 'https://api.eeil.online/uploads/connector_type/wall.svg',
    },
  };

  // Legacy mapping for backward compatibility
  static Map<String, String> get connectorTypeMapping =>
      connectorTypeData.map((key, value) => MapEntry(key, value['standard']!));

  // Get display names for UI
  static List<String> get availableConnectorTypes =>
      connectorTypeData.keys.toList();

  // Convert display names to standard codes for API
  static List<String> getStandardCodes(List<String> displayNames) {
    return displayNames
        .map((name) => connectorTypeData[name]?['standard'])
        .where((code) => code != null && code.isNotEmpty)
        .cast<String>()
        .toList();
  }

  // Get icon URL for a connector type
  static String? getConnectorIconUrl(String displayName) {
    final iconUrl = connectorTypeData[displayName]?['iconUrl'];
    return (iconUrl != null && iconUrl.isNotEmpty) ? iconUrl : null;
  }

  // Get all connector icon URLs for preloading
  static List<String> getAllConnectorIconUrls() {
    return connectorTypeData.values
        .map((data) => data['iconUrl']!)
        .where((url) => url.isNotEmpty)
        .toList();
  }

  final Map<String, bool> selectedConnectorFilters;
  final String selectedPowerOutput;
  final Function(Map<String, bool>, String) onApplyFilters;

  const FilterDialog({
    super.key,
    required this.selectedConnectorFilters,
    required this.selectedPowerOutput,
    required this.onApplyFilters,
  });

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  late Map<String, bool> _selectedConnectorFilters;
  late String _selectedPowerOutput; // Changed from bool _showOnlyAvailable
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    // Create a copy of the filters to avoid modifying the original
    _selectedConnectorFilters = Map.from(widget.selectedConnectorFilters);
    _selectedPowerOutput =
        widget.selectedPowerOutput; // Changed from showOnlyAvailable
  }

  void _checkForChanges() {
    bool connectorChanges = false;

    for (var key in widget.selectedConnectorFilters.keys) {
      if (widget.selectedConnectorFilters[key] !=
          _selectedConnectorFilters[key]) {
        connectorChanges = true;
        break;
      }
    }

    setState(() {
      _hasChanges = connectorChanges ||
          widget.selectedPowerOutput !=
              _selectedPowerOutput; // Updated comparison
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = AppThemes.primaryColor;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: isDarkMode
            ? BorderSide(color: AppThemes.primaryColor.withAlpha(50), width: 1)
            : BorderSide.none,
      ),
      elevation: isDarkMode ? 0 : 8,
      backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppThemes.primaryColor.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.filter_alt_rounded,
                    color: AppThemes.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Filter Stations',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const Spacer(),
                Container(
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? const Color(0xFF262626)
                        : Colors.grey.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.close, size: 20),
                    onPressed: () => Navigator.pop(context),
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Power Output filter
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.bolt_rounded,
                    color: primaryColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Power Output',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Power output selection chips
            Row(
              children: [
                Expanded(
                  child: _buildPowerOutputChip('All', isDarkMode, primaryColor),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildPowerOutputChip('AC', isDarkMode, primaryColor),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildPowerOutputChip('DC', isDarkMode, primaryColor),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Connector types
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.electrical_services_rounded,
                    color: primaryColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Connector Types',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Connector type grid - 3 columns with reduced spacing
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // Changed from 2 to 3 columns
                crossAxisSpacing: 8, // Reduced from 12 to 8
                mainAxisSpacing: 8, // Reduced from 12 to 8
                childAspectRatio: 1.0, // Adjusted for better proportions
              ),
              itemCount: _selectedConnectorFilters.length,
              itemBuilder: (context, index) {
                final entry =
                    _selectedConnectorFilters.entries.elementAt(index);
                return _buildConnectorGridItem(
                  entry.key,
                  entry.value,
                  isDarkMode,
                  primaryColor,
                );
              },
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                OutlinedButton.icon(
                  onPressed: () {
                    // Reset all filters
                    setState(() {
                      for (var key in _selectedConnectorFilters.keys) {
                        _selectedConnectorFilters[key] = false;
                      }
                      _selectedPowerOutput =
                          'All'; // Reset to 'All' instead of false
                    });
                    _checkForChanges();
                  },
                  icon: Icon(
                    Icons.refresh_rounded,
                    size: 18,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                  label: Text(
                    'Reset All',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _hasChanges
                      ? () {
                          widget.onApplyFilters(_selectedConnectorFilters,
                              _selectedPowerOutput); // Updated to use power output
                          Navigator.pop(context);
                        }
                      : null,
                  icon: const Icon(
                    Icons.check_rounded,
                    size: 18,
                  ),
                  label: const Text(
                    'Apply Filters',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade300,
                    disabledForegroundColor: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Build power output selection chip
  Widget _buildPowerOutputChip(
      String option, bool isDarkMode, Color primaryColor) {
    final isSelected = _selectedPowerOutput == option;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPowerOutput = option;
        });
        _checkForChanges();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected
              ? primaryColor
              : (isDarkMode ? const Color(0xFF262626) : Colors.grey.shade50),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? primaryColor
                : (isDarkMode ? const Color(0xFF2E2E2E) : Colors.grey.shade200),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Center(
          child: Text(
            option,
            style: TextStyle(
              color: isSelected
                  ? Colors.white
                  : (isDarkMode ? Colors.white70 : Colors.black87),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  // Build connector grid item with improved tap target and dynamic support
  Widget _buildConnectorGridItem(String connectorType, bool isSelected,
      bool isDarkMode, Color primaryColor) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            // Single selection mode: clear all other selections first
            for (String key in _selectedConnectorFilters.keys) {
              _selectedConnectorFilters[key] = false;
            }
            // Then select the tapped connector (or deselect if it was already selected)
            _selectedConnectorFilters[connectorType] = !isSelected;
          });
          _checkForChanges();
        },
        borderRadius: BorderRadius.circular(8),
        // Increased tap target area with padding
        child: Container(
          padding:
              const EdgeInsets.all(8), // Adds tap target area around content
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Display actual connector icon from API or fallback to placeholder
              _buildConnectorIcon(
                connectorType,
                isSelected,
                isDarkMode,
                primaryColor,
              ),
              const SizedBox(height: 6), // Reduced spacing for compact layout
              // Connector name - supports dynamic connector types from API
              Text(
                connectorType,
                style: TextStyle(
                  color: isSelected
                      ? primaryColor
                      : (isDarkMode ? Colors.white70 : Colors.black87),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 11, // Slightly smaller for 3-column layout
                ),
                textAlign: TextAlign.center,
                maxLines: 2, // Allow wrapping for longer connector names
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build connector icon with SVG support and fallback - displays original icon colors
  Widget _buildConnectorIcon(String connectorType, bool isSelected,
      bool isDarkMode, Color primaryColor) {
    final iconUrl = FilterDialog.getConnectorIconUrl(connectorType);

    if (iconUrl != null) {
      // Display SVG icon from API in original colors
      return Container(
        width: 28,
        height: 28,
        decoration: isSelected
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: primaryColor,
                  width: 2,
                ),
                color: primaryColor.withAlpha(20),
              )
            : null,
        padding: EdgeInsets.zero, // Remove padding adjustment for size changes
        child: SvgPicture.network(
          iconUrl,
          width: 28, // Keep consistent size for all icons
          height: 28, // Keep consistent size for all icons
          // No color filtering - display original icon colors
          placeholderBuilder: (context) => SizedBox(
            width: 28, // Keep consistent size for all icons
            height: 28, // Keep consistent size for all icons
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          // Fallback to placeholder icon if SVG fails to load
          errorBuilder: (context, error, stackTrace) {
            debugPrint(
                'Failed to load connector icon for $connectorType: $error');
            return Icon(
              Icons.electrical_services_rounded,
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              size: 28, // Keep consistent size for all icons
            );
          },
        ),
      );
    } else {
      // Fallback to placeholder icon for connector types without specific icons
      return Container(
        width: 28,
        height: 28,
        decoration: isSelected
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: primaryColor,
                  width: 2,
                ),
                color: primaryColor.withAlpha(20),
              )
            : null,
        padding: EdgeInsets.zero, // Remove padding adjustment for size changes
        child: Icon(
          Icons.electrical_services_rounded,
          color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
          size: 28, // Keep consistent size for all icons
        ),
      );
    }
  }
}
