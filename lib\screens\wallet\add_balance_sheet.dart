import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/app_themes.dart';
import '../../models/promo_code.dart';
import '../../services/auth_manager.dart';

/// A professional bottom sheet for adding balance to the wallet
/// Features modern UI, smooth validation, and excellent user experience
class AddBalanceSheet extends StatefulWidget {
  /// Callback function when user confirms adding balance
  final Function(double amount, {String source, String? promocode})
      onAddBalance;

  /// Offer message data from wallet response
  final Map<String, dynamic>? offerMessage;

  const AddBalanceSheet({
    super.key,
    required this.onAddBalance,
    this.offerMessage,
  });

  @override
  State<AddBalanceSheet> createState() => _AddBalanceSheetState();
}

class _AddBalanceSheetState extends State<AddBalanceSheet>
    with TickerProviderStateMixin {
  // Text controller for the amount input
  final TextEditingController _amountController = TextEditingController();

  // Animation controller for smooth animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Minimum amount configuration - REMOVED RESTRICTION
  static const double minimumAmount = 1.0; // Changed from 50.0 to 1.0
  static const double maximumAmount = 50000.0;

  // Quick selection amounts (starting with minimum)
  final List<int> _quickAmounts = [
    50,
    100,
    500,
    1000
  ]; // Removed 1 and 10 rupees options

  // Currently selected quick amount (null if custom amount)
  int? _selectedAmount;

  // Validation state
  String? _errorMessage;

  // Server-determined payment gateway (no user selection needed)
  final String _serverSelectedGateway = 'server_determined';

  // Focus node for the text field
  final FocusNode _focusNode = FocusNode();

  // Promo code input controller
  final TextEditingController _promoCodeController = TextEditingController();

  // Promo code state
  bool _isPromoApplied = false;
  String? _appliedPromoCode;
  double _promoBonus = 0.0;
  String? _successMessage; // For displaying API success message

  // Promo code selection state
  List<PromoCode> _availablePromoCodes = [];
  bool _isLoadingPromoCodes = false;
  String? _promoCodeError;

  // Promo code verification state
  Map<String, dynamic>? _verifiedPromo;
  int _minAmountApplicable = 0;
  bool _isVerifyingPromo = false;

  // State to track if promo code was manually entered or selected from list
  bool _promoCodeManuallyEntered = false;

  // Enhanced promo code selection state
  String _searchQuery = '';

  // Focus node for promo code input
  final FocusNode _promoFocusNode = FocusNode();

  // Animation controllers for smooth transitions
  late AnimationController _promoAnimationController;
  late Animation<double> _promoFadeAnimation;
  late Animation<Offset> _promoSlideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Initialize promo animation controller
    _promoAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // Initialize animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
    ));

    // Initialize promo animations
    _promoFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _promoAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _promoSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _promoAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Start the animation
    _animationController.forward();

    // Add listener for amount validation
    _amountController.addListener(_validateAmount);

    // Focus listener for UI updates
    _focusNode.addListener(() {
      setState(() {
        // Trigger rebuild for focus-dependent styling
      });
    });

    // Focus listener for promo input
    _promoFocusNode.addListener(() {
      setState(() {
        // Trigger rebuild for focus-dependent styling
      });
    });
  }

  @override
  void dispose() {
    // Reset loading state when disposing
    _isLoadingPromoCodes = false;
    _amountController.removeListener(_validateAmount);
    _amountController.dispose();
    _promoCodeController.dispose();
    _animationController.dispose();
    _promoAnimationController.dispose();
    _focusNode.dispose();
    _promoFocusNode.dispose();
    super.dispose();
  }

  // Smooth, professional validation without jarring feedback
  void _validateAmount() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null;
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = 'Please enter a valid number';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null; // Don't show error immediately for better UX
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Amount is valid
    setState(() {
      _errorMessage = null;

      // Check if matches quick amount
      final amountInt = amount.toInt();
      if (amount == amountInt && _quickAmounts.contains(amountInt)) {
        _selectedAmount = amountInt;
      } else {
        _selectedAmount = null;
      }
    });
  }

  // Handle quick amount selection with smooth animation
  void _selectQuickAmount(int amount) {
    setState(() {
      _selectedAmount = amount;
      _amountController.text = amount.toString();
      _errorMessage = null;
    });

    // Smooth unfocus
    _focusNode.unfocus();
  }

  // Professional validation and submission with inline feedback
  void _handleAddBalance() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an amount';
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ₹${minimumAmount.toInt()}';
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Check if promo code is applied but minimum amount is not met
    if (_isPromoApplied && _verifiedPromo != null) {
      final minRequired = _verifiedPromo!['minimum_amount_applicable'] ?? 0;
      if (minRequired > 0 && amount < minRequired) {
        setState(() {
          _errorMessage = 'Minimum amount of ₹$minRequired required for this promo code';
        });
        return;
      }
    }

    // Clear any error messages
    setState(() {
      _errorMessage = null;
    });

    // Success - proceed with payment (server will determine payment gateway)
    // Pass both original amount and promo details
    widget.onAddBalance(
      amount, // Original amount for payment
      source: _serverSelectedGateway,
      promocode:
          _verifiedPromo?['code'], // Include verified promo code if available
    );
  }

  // Get input background color with enhanced contrast
  Color _getInputBackgroundColor(bool isDarkMode) {
    if (_focusNode.hasFocus) {
      return const Color(0xFF4776E6)
          .withValues(alpha: 0.05); // Use blue background when focused
    }
    // Default background colors for non-focused state
    return isDarkMode ? AppThemes.darkCard : Colors.grey.shade50;
  }

  // Build promo code minimum amount indicator
  Widget _buildPromoMinimumIndicator(bool isDarkMode, Color primaryColor, Color secondaryColor) {
    final currentAmount = double.tryParse(_amountController.text) ?? 0.0;
    final minRequired = _verifiedPromo!['minimum_amount_applicable'] ?? 0;
    final amountNeeded = minRequired - currentAmount;
    
    if (minRequired <= 0 || currentAmount >= minRequired) {
      // Show success indicator when minimum is met
      return Container(
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.green.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Colors.green.shade600,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Promo code minimum amount requirement met!',
                style: TextStyle(
                  color: Colors.green.shade700,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    // Show warning when minimum is not met
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.orange.shade600,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Add ₹${amountNeeded.toInt()} more to use this promo code (Min: ₹$minRequired)',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _amountController.text = minRequired.toString();
                _selectedAmount = null; // Clear quick selection
                _errorMessage = null;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange.shade600,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'Set ₹$minRequired',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build custom input box for amount entry with solid blue border
  Widget _buildCustomInputBox(bool isDarkMode) {
    return Container(
      height: 58, // Fixed height
      decoration: BoxDecoration(
        color: _getInputBackgroundColor(isDarkMode),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _focusNode.hasFocus
              ? const Color(0xFF4776E6) // Solid blue when focused
              : (isDarkMode
                  ? Colors.grey.shade600
                  : Colors.grey.shade300), // Gray when not focused
          width: 2.0, // Consistent border width
        ),
      ),
      child: Row(
        children: [
          // Rupee symbol container
          Container(
            width: 60,
            alignment: Alignment.center,
            child: Text(
              '₹',
              style: TextStyle(
                color: _focusNode.hasFocus
                    ? const Color(0xFF4776E6) // Blue when focused
                    : (isDarkMode
                        ? Colors.grey.shade400
                        : Colors.grey.shade600), // Gray when not focused
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Text input field
          Expanded(
            child: TextField(
              controller: _amountController,
              focusNode: _focusNode,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              style: TextStyle(
                color: isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF1A1A1A),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                hintText: 'Enter amount (₹${minimumAmount.toInt()} or more)',
                hintStyle: TextStyle(
                  color:
                      isDarkMode ? Colors.grey.shade500 : Colors.grey.shade500,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none, // Remove all borders
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 0,
                  vertical: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16), // Right padding
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    // Use app's primary lime color consistently
    final primaryColor = AppThemes.primaryColor; // Lime green #8cc051
    final secondaryColor = AppThemes.secondaryColor; // Blue #3D7AF5
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.9,
          ),
          decoration: BoxDecoration(
            color: isDarkMode ? AppThemes.darkSurface : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Scrollable content area
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                      left: 24,
                      right: 24,
                      top: 16,
                      bottom: keyboardHeight > 0 ? 16 : 0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Modern sheet handle
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Center(
                            child: Container(
                              width: 48,
                              height: 4,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade400,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Professional title
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              children: [
                                Text(
                                  'Add Balance',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w700,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Add money to your wallet securely',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: isDarkMode
                                        ? AppThemes.darkTextSecondary
                                        : Colors.grey.shade600,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Amount input field
                        const SizedBox(height: 32),

                        // Amount input section
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Amount input label
                                Text(
                                  'Enter Amount',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                  ),
                                ),
                                const SizedBox(height: 12),

                                // Custom amount input box
                                _buildCustomInputBox(isDarkMode),

                                // Promo code minimum amount indicator
                                if (_isPromoApplied && _verifiedPromo != null)
                                  _buildPromoMinimumIndicator(isDarkMode, primaryColor, secondaryColor),

                                // Error message display
                                if (_errorMessage != null) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    _errorMessage!,
                                    style: TextStyle(
                                      color: Colors.red.shade600,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],

                                const SizedBox(height: 20),

                                // Quick amount selection
                                Text(
                                  'Quick Select',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                  ),
                                ),
                                const SizedBox(height: 12),

                                // Quick amount buttons
                                Wrap(
                                  spacing: 12,
                                  runSpacing: 8,
                                  children: _quickAmounts.map((amount) {
                                    return _buildModernQuickAmountButton(
                                      amount: amount,
                                      isSelected: _selectedAmount == amount,
                                      onTap: () => _selectQuickAmount(amount),
                                      primaryColor: primaryColor,
                                      secondaryColor: secondaryColor,
                                      isDarkMode: isDarkMode,
                                    );
                                  }).toList(),
                                ),

                                const SizedBox(height: 20),

                                // Comprehensive promo code section
                                _buildPromoCodeSection(
                                    isDarkMode, primaryColor, secondaryColor),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),

                // Fixed bottom section with validation and button
                Container(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    border: Border(
                      top: BorderSide(
                        color: isDarkMode
                            ? AppThemes.darkBorder
                            : Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Validation message area (always present but conditionally visible)
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: _errorMessage != null ? 40 : 0,
                        child: _errorMessage != null
                            ? Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.red.shade900
                                          .withValues(alpha: 0.2)
                                      : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.red.shade400,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.warning_amber_rounded,
                                      color: Colors.red.shade600,
                                      size: 18,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _errorMessage!,
                                        style: TextStyle(
                                          color: isDarkMode
                                              ? Colors.red.shade300
                                              : Colors.red.shade700,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),

                      const SizedBox(height: 16),

                      // Show total amount if promo is applied
                      if (_isPromoApplied && _promoBonus > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                primaryColor.withValues(alpha: 0.1),
                                secondaryColor.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: primaryColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Payment Amount: ₹${_amountController.text}',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    'Bonus Amount: +₹${_promoBonus.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: Colors.green.shade600,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Total Balance',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    '₹${(double.tryParse(_amountController.text) ?? 0 + _promoBonus).toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextPrimary
                                          : const Color(0xFF1A1A1A),
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 16),

                      // Always visible add balance button
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildModernAddBalanceButton(
                              primaryColor, secondaryColor, isDarkMode),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Modern quick amount button with professional styling
  Widget _buildModernQuickAmountButton({
    required int amount,
    required bool isSelected,
    required VoidCallback onTap,
    required Color primaryColor,
    required Color secondaryColor,
    required bool isDarkMode,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(
            horizontal: 18,
            vertical: 10), // Reduced padding for more compact design
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [primaryColor, secondaryColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected
              ? null
              : (isDarkMode ? AppThemes.darkCard : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Text(
          '₹$amount',
          style: TextStyle(
            color: isSelected
                ? Colors.white
                : (isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF333333)),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  // Comprehensive promo code section with all states
  Widget _buildPromoCodeSection(
      bool isDarkMode, Color primaryColor, Color secondaryColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Promo Code',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: isDarkMode
                ? AppThemes.darkTextPrimary
                : const Color(0xFF1A1A1A),
          ),
        ),
        const SizedBox(height: 12),

        // Promo code input/display area
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (child, animation) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          child: _isVerifyingPromo
              ? _buildVerifyingPromoState(isDarkMode, primaryColor)
              : _isPromoApplied && _appliedPromoCode != null
                  ? _buildAppliedPromoState(
                      isDarkMode, primaryColor, secondaryColor)
                  : _buildPromoCodeInput(
                      isDarkMode, primaryColor, secondaryColor),
        ),
      ],
    );
  }

  // Verifying promo code state
  Widget _buildVerifyingPromoState(bool isDarkMode, Color primaryColor) {
    return Container(
      key: const ValueKey('verifying_promo'),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: primaryColor.withOpacity(0.5),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Verifying Code...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: isDarkMode
                  ? AppThemes.darkTextPrimary
                  : const Color(0xFF1A1A1A),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced applied promo code state with animations and success message
  Widget _buildAppliedPromoState(
      bool isDarkMode, Color primaryColor, Color secondaryColor) {
    return Container(
      key: const ValueKey('applied_promo'),
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withOpacity(0.1),
            secondaryColor.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withOpacity(0.6),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _successMessage ?? 'Promo Code Applied',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            _appliedPromoCode!,
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.green.shade700,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '₹${_promoBonus.toInt()} OFF',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.red.shade600),
                  onPressed: _removePromoCode,
                  tooltip: 'Remove promo code',
                ),
              ],
            ),
          ),
          const Divider(height: 1, color: Colors.green),
          InkWell(
            onTap: () {
              setState(() {
                _isPromoApplied = false;
                _appliedPromoCode = null;
                _promoBonus = 0.0;
                _verifiedPromo = null;
                _successMessage = null;
                _promoCodeController.clear();
              });
              _showPromoCodeSelection();
            },
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.edit,
                    color: primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Change Promo Code',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced promo code input state with modern styling
  Widget _buildPromoCodeInput(
      bool isDarkMode, Color primaryColor, Color secondaryColor) {
    final bool hasText = _promoCodeController.text.isNotEmpty;
    final bool hasError = _promoCodeError != null;

    return Card(
      key: const ValueKey('input_promo'),
      elevation: 2,
      shadowColor: isDarkMode
          ? Colors.black.withOpacity(0.5)
          : Colors.grey.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _promoCodeController,
                        focusNode: _promoFocusNode,
                        style: TextStyle(
                          color: hasError
                              ? Colors.red.shade700
                              : (isDarkMode
                                  ? AppThemes.darkTextPrimary
                                  : const Color(0xFF1A1A1A)),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter Promo Code',
                          prefixIcon: Icon(Icons.local_offer_outlined,
                              color: primaryColor),
                          border: InputBorder.none,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 12),
                        ),
                        textCapitalization: TextCapitalization.characters,
                        onChanged: (value) {
                          setState(() {
                            if (_promoCodeError != null) {
                              _promoCodeError = null;
                            }
                          });
                        },
                        onSubmitted: (value) {
                          if (value.trim().isNotEmpty) {
                            _verifyPromoCode(value.trim());
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: hasText
                          ? () {
                              final code = _promoCodeController.text.trim();
                              if (code.isNotEmpty) {
                                _verifyPromoCode(code);
                              }
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: secondaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                      ),
                      child: const Text(
                        'Apply',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                if (hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _promoCodeError!,
                      style: TextStyle(
                        color: Colors.red.shade600,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const Divider(height: 1),
          InkWell(
            onTap: _showPromoCodeSelection,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search,
                    color: primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Browse Available Promo Codes',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Modern add balance button - always visible with professional styling
  Widget _buildModernAddBalanceButton(
      Color primaryColor, Color secondaryColor, bool isDarkMode) {
    // Button is always enabled but shows different states
    final canProceed = _amountController.text.isNotEmpty;

    return Container(
      height: 52, // Reduced from 56 to 52 for more compact design
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [secondaryColor, secondaryColor], // Use only blue
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleAddBalance(),
          borderRadius: BorderRadius.circular(16),
          splashColor: Colors.white.withValues(alpha: 0.2),
          highlightColor: Colors.white.withValues(alpha: 0.1),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (!canProceed) ...[
                  Icon(
                    Icons.info_outline,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  canProceed ? 'Add Balance' : 'Enter Amount to Continue',
                  style: TextStyle(
                    color: canProceed
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    letterSpacing: -0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Remove promo code with animation reset
  void _removePromoCode() {
    // Reset animation
    _promoAnimationController.reset();

    setState(() {
      _isPromoApplied = false;
      _appliedPromoCode = null;
      _promoBonus = 0.0;
      _verifiedPromo = null;
      _minAmountApplicable = 0;
      _successMessage = null; // Clear success message
      _promoCodeController.clear();
    });
  }

  // Show warning when user tries to select promo code without meeting minimum amount
  void _showMinimumAmountWarning(int minimumAmount) {
    final currentAmount = double.tryParse(_amountController.text) ?? 0.0;
    final amountNeeded = minimumAmount - currentAmount.toInt();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Minimum Amount Required',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    'Add ₹$amountNeeded more to use this promo code (Min: ₹$minimumAmount)',
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        action: SnackBarAction(
          label: 'Add ₹$minimumAmount',
          textColor: Colors.white,
          onPressed: () {
            // Auto-fill the minimum amount
            setState(() {
              _amountController.text = minimumAmount.toString();
              _selectedAmount = null; // Clear quick selection
              _errorMessage = null;
            });
            // Close the promo selection sheet if it's open
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        ),
      ),
    );
  }

  // Verify promo code with server
  Future<void> _verifyPromoCode(String promoCode,
      {bool autoApply = true}) async {
    if (kDebugMode) {
      debugPrint('🔔 PROMO: Starting verification for code: $promoCode');
      debugPrint('🔔 PROMO: Auto-apply: $autoApply');
    }

    // Only verify if promo code length > 3 characters
    if (promoCode.length <= 3) {
      setState(() {
        _promoCodeError = 'Promo code must be more than 3 characters';
      });
      return;
    }

    // Don't re-verify if the same promo code is already applied
    if (_isPromoApplied && _appliedPromoCode == promoCode.toUpperCase()) {
      return;
    }

    // Set loading state
    setState(() {
      _isVerifyingPromo = true;
      _promoCodeError = null;
    });

    try {
      // Get user's auth token using AuthManager for consistency
      final authManager = AuthManager();
      final token = await authManager.getToken();

      if (kDebugMode) {
        debugPrint(
            '🔔 PROMO: Token from AuthManager: ${token != null ? 'Found' : 'Not found'}');
      }

      // Make HTTP POST request to the CORRECT verify endpoint
      final response = await http.post(
        Uri.parse(
            'https://api2.eeil.online/api/v1/user/promocodes/verify?promo=${promoCode.toUpperCase()}'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      final responseData = json.decode(response.body);

      // Add debug logging for API response
      if (kDebugMode) {
        debugPrint('🔔 PROMO: API Response Status: ${response.statusCode}');
        debugPrint('🔔 PROMO: API Response Body: ${response.body}');
        debugPrint('🔔 PROMO: Parsed Response: $responseData');
      }

      if (response.statusCode == 200 && responseData['success'] == true) {
        // Success response - store verified promo data and success message
        setState(() {
          _verifiedPromo = responseData['data'];
          _minAmountApplicable =
              _verifiedPromo?['minimum_amount_applicable'] ?? 0;
          _isVerifyingPromo = false;
          _promoCodeError = null;
          // Capture the success message from API response
          _successMessage = responseData['message'] ?? 'Coupon Applied!';
        });

        // Don't automatically close promo selection modals - let the caller handle it

        // Apply or update the promo code based on autoApply parameter
        if (autoApply) {
          // For manual entry - apply the verified promo code
          _applyVerifiedPromoCode();
        } else {
          // For selection from list - just update the bonus (promo code is already applied to UI)
          setState(() {
            _promoBonus = (_verifiedPromo!['credits'] ?? 0).toDouble();
          });
        }

        // Trigger success animation
        _promoAnimationController.forward();

        // Check if current amount meets minimum requirement
        final currentAmount = double.tryParse(_amountController.text) ?? 0.0;
        final minAmount = _minAmountApplicable;

        // Show appropriate feedback
        if (mounted) {
          if (minAmount > 0 && currentAmount < minAmount) {
            // Show warning about minimum amount
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Promo code verified! Minimum amount required: ₹$minAmount'),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 4),
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            // Show success feedback
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Promo code verified! You saved ₹${_verifiedPromo!['credits']}'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      } else {
        // Error response - remove promo code from UI since it's invalid
        setState(() {
          _isVerifyingPromo = false;
          _promoCodeError = responseData['message'] ?? 'Invalid promo code';
          _verifiedPromo = null;
          _minAmountApplicable = 0;
          // Remove invalid promo code from UI
          _isPromoApplied = false;
          _appliedPromoCode = null;
          _promoBonus = 0.0;
          _promoCodeController.clear();
        });

        // Show error feedback
        if (mounted) {
          final errorMsg = autoApply
              ? (_promoCodeError ?? 'Invalid promo code')
              : 'Promo code ${promoCode.toUpperCase()} is not valid or has expired';
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error_outline,
                      color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                  Expanded(child: Text(errorMsg)),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Network or other errors - remove promo code from UI since verification failed
      if (kDebugMode) {
        debugPrint('🔔 PROMO: Error during verification: $e');
        debugPrint('🔔 PROMO: Error type: ${e.runtimeType}');
      }

      setState(() {
        _isVerifyingPromo = false;
        _promoCodeError =
            'Network error. Please check your connection and try again.';
        _verifiedPromo = null;
        _minAmountApplicable = 0;
        // Remove promo code from UI due to network error
        _isPromoApplied = false;
        _appliedPromoCode = null;
        _promoBonus = 0.0;
        _promoCodeController.clear();
      });

      // Show network error feedback
      if (mounted) {
        final errorMsg = autoApply
            ? 'Network error. Please check your connection and try again.'
            : 'Unable to verify promo code ${promoCode.toUpperCase()}. Please check your connection.';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMsg),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Apply verified promo code to the UI with animation
  void _applyVerifiedPromoCode() {
    if (_verifiedPromo == null) return;

    setState(() {
      _isPromoApplied = true;
      _appliedPromoCode = _verifiedPromo!['code'];
      _promoCodeController.text = _verifiedPromo!['code'];
      _promoBonus = (_verifiedPromo!['credits'] ?? 0).toDouble();
    });

    // Trigger success animation
    _promoAnimationController.forward();
  }

  // Show celebration effect when promo code is successfully applied
  void _showCelebrationEffect() {
    if (!mounted) return;

    // Trigger haptic feedback
    HapticFeedback.lightImpact();

    // Show confetti-style celebration overlay
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.transparent,
      builder: (context) => _CelebrationOverlay(
        onComplete: () {
          Navigator.of(context).pop();
        },
      ),
    );

    // Show success snackbar with celebration
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.celebration, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Promo code applied! You saved ₹${_promoBonus.toStringAsFixed(0)}',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        action: SnackBarAction(
          label: '🎉',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  // Handle promo code selection with verification within the sheet
  Future<void> _handlePromoCodeSelection(PromoCode promo) async {
    if (kDebugMode) {
      debugPrint('🔔 PROMO: Handling selection for code: ${promo.code}');
    }

    // Update the main sheet's promo code field
    setState(() {
      _promoCodeController.text = promo.code.toUpperCase();
      _isVerifyingPromo = true;
      _promoCodeError = null;
      _successMessage = null;
    });

    try {
      // Verify the selected promo code
      await _verifyPromoCode(promo.code, autoApply: true);

      // If verification was successful, close the promo selection sheet
      if (mounted && _verifiedPromo != null && Navigator.canPop(context)) {
        Navigator.pop(context);

        // Show celebration effect
        _showCelebrationEffect();
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('🔔 PROMO: Error during selection handling: $e');
      }
      // Error handling is already done in _verifyPromoCode
    }
  }

  // Enhanced full-screen promo code selection modal
  Future<void> _showPromoCodeSelection() async {
    // Fetch if needed
    if (_availablePromoCodes.isEmpty &&
        !_isLoadingPromoCodes &&
        _promoCodeError == null) {
      await _fetchPromoCodes();
    }

    if (!mounted) return;

    // Reset search state
    _searchQuery = '';

    await showModalBottomSheet<PromoCode>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => _PromoCodeSelectionSheet(
        availablePromoCodes: _availablePromoCodes,
        isLoadingPromoCodes: _isLoadingPromoCodes,
        promoCodeError: _promoCodeError,
        searchQuery: _searchQuery,
        isVerifyingPromo: _isVerifyingPromo,
        verifiedPromo: _verifiedPromo,
        currentAmount: double.tryParse(_amountController.text) ?? 0.0,
        onPromoSelected: (promo) async {
          // Don't close the sheet immediately - handle verification within the sheet
          await _handlePromoCodeSelection(promo);
        },
        onRetry: () async {
          Navigator.pop(context);
          await _fetchPromoCodes();
          if (mounted) _showPromoCodeSelection();
        },
        onSearchChanged: (query) {
          // Don't use setState here as it rebuilds the parent widget
          _searchQuery = query;
        },
      ),
    );

    // Note: Promo code selection is now handled within the sheet itself
    // via _handlePromoCodeSelection method, so no additional logic needed here
  }

  Future<void> _fetchPromoCodes() async {
    setState(() {
      _isLoadingPromoCodes = true;
      _promoCodeError = null; // Clear previous errors to allow retrying
    });

    try {
      // Get user's auth token using AuthManager for consistency
      final authManager = AuthManager();
      final token = await authManager.getToken();

      final response = await http.get(
        Uri.parse(
            'https://api2.eeil.online/api/v1/user/promocodes?promo_type=recharge'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (mounted) {
        // Check if the widget is still in the tree
        if (response.statusCode == 200) {
          final jsonData = json.decode(response.body);

          // Check for success and if data is a non-empty list
          if (jsonData['success'] == true &&
              jsonData['data'] != null &&
              (jsonData['data'] as List).isNotEmpty) {
            final List<dynamic> promoData = jsonData['data'];
            final promoCodes =
                promoData.map((item) => PromoCode.fromJson(item)).toList();

            setState(() {
              _availablePromoCodes = promoCodes;
              _isLoadingPromoCodes = false;
              _promoCodeError = null;
            });
          } else {
            // API returned success but no data or an empty list
            final errorMessage = jsonData['message'] ??
                'No promo codes are available at the moment.';

            setState(() {
              _promoCodeError = errorMessage;
              _availablePromoCodes = [];
              _isLoadingPromoCodes = false;
            });
          }
        } else if (response.statusCode == 401) {
          // Unauthorized - token issue

          setState(() {
            _promoCodeError = 'Please log in again to access promo codes';
            _isLoadingPromoCodes = false;
            _availablePromoCodes = [];
          });

          // Clear the invalid token using AuthManager
          final authManager = AuthManager();
          await authManager.logout();
        } else {
          // Handle other non-200 responses

          // Try to parse error message from response if possible
          String errorMsg = 'Failed to load promo codes';
          try {
            final errorData = json.decode(response.body);
            if (errorData['message'] != null) {
              errorMsg = errorData['message'];
            }
          } catch (_) {
            // If parsing fails, use default message with status code
            errorMsg =
                'Failed to load promo codes (Error: ${response.statusCode})';
          }

          setState(() {
            _promoCodeError = errorMsg;
            _isLoadingPromoCodes = false;
            _availablePromoCodes = [];
          });
        }
      }
    } catch (e) {
      if (mounted) {
        // Check if the widget is still in the tree
        // Handle exceptions like network errors

        setState(() {
          _promoCodeError =
              'Unable to connect to the server. Please check your internet connection and try again.';
          _isLoadingPromoCodes = false;
          _availablePromoCodes = [];
        });
      }
    }
  }
}

/// Enhanced full-screen promo code selection sheet
class _PromoCodeSelectionSheet extends StatefulWidget {
  final List<PromoCode> availablePromoCodes;
  final bool isLoadingPromoCodes;
  final String? promoCodeError;
  final String searchQuery;
  final bool isVerifyingPromo;
  final Map<String, dynamic>? verifiedPromo;
  final double currentAmount;
  final Function(PromoCode) onPromoSelected;
  final VoidCallback onRetry;
  final Function(String) onSearchChanged;

  const _PromoCodeSelectionSheet({
    required this.availablePromoCodes,
    required this.isLoadingPromoCodes,
    required this.promoCodeError,
    required this.searchQuery,
    required this.isVerifyingPromo,
    required this.verifiedPromo,
    required this.currentAmount,
    required this.onPromoSelected,
    required this.onRetry,
    required this.onSearchChanged,
  });

  @override
  State<_PromoCodeSelectionSheet> createState() =>
      _PromoCodeSelectionSheetState();
}

class _PromoCodeSelectionSheetState extends State<_PromoCodeSelectionSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenHeight = MediaQuery.of(context).size.height;
    final primaryColor = AppThemes.primaryColor;
    final secondaryColor = AppThemes.secondaryColor;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * screenHeight),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Stack(
              children: [
                Container(
                  height: screenHeight,
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(24)),
                  ),
                  child: SafeArea(
                    child: Column(
                      children: [
                        // Header with title and close button
                        _buildHeader(isDarkMode, primaryColor),

                        // Content area
                        Expanded(
                          child: _buildContent(
                              isDarkMode, primaryColor, secondaryColor),
                        ),
                      ],
                    ),
                  ),
                ),

                // Loading overlay when verifying promo code
                if (widget.isVerifyingPromo)
                  Container(
                    color: Colors.black.withValues(alpha: 0.5),
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: isDarkMode ? AppThemes.darkCard : Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const CircularProgressIndicator(),
                            const SizedBox(height: 16),
                            Text(
                              'Verifying promo code...',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Please wait while we validate your promo code',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode
                                    ? Colors.grey[300]
                                    : Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(bool isDarkMode, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'AVAILABLE Promo Codes',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: isDarkMode
                        ? AppThemes.darkTextPrimary
                        : const Color(0xFF1A1A1A),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Select a promo code to get extra savings',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDarkMode ? AppThemes.darkCard : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.close,
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : Colors.grey.shade600,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationErrorBanner(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Verification Failed',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.promoCodeError ?? 'Unknown error occurred',
                  style: TextStyle(
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Clear the error by triggering a rebuild
              setState(() {});
            },
            icon: Icon(
              Icons.close,
              color: Colors.red,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
      bool isDarkMode, Color primaryColor, Color secondaryColor) {
    if (widget.isLoadingPromoCodes) {
      return _buildLoadingState(isDarkMode);
    }

    if (widget.promoCodeError != null) {
      return _buildErrorState(isDarkMode, primaryColor);
    }

    if (widget.availablePromoCodes.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    // Filter promo codes based on search query
    final filteredPromoCodes = widget.availablePromoCodes.where((promo) {
      if (widget.searchQuery.isEmpty) return true;
      return promo.code
              .toLowerCase()
              .contains(widget.searchQuery.toLowerCase()) ||
          promo.description
              .toLowerCase()
              .contains(widget.searchQuery.toLowerCase());
    }).toList();

    return Column(
      children: [
        // Verification error banner
        if (widget.promoCodeError != null && widget.isVerifyingPromo == false)
          _buildVerificationErrorBanner(isDarkMode),

        // Search bar
        if (widget.availablePromoCodes.length > 3) _buildSearchBar(isDarkMode),

        // Promo codes list
        Expanded(
          child: filteredPromoCodes.isEmpty
              ? _buildNoSearchResults(isDarkMode)
              : _buildPromoCodesList(
                  filteredPromoCodes, isDarkMode, primaryColor, secondaryColor),
        ),
      ],
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppThemes.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading promo codes...',
            style: TextStyle(
              color: isDarkMode
                  ? AppThemes.darkTextSecondary
                  : Colors.grey.shade600,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode, Color primaryColor) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load promo codes',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF1A1A1A),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.promoCodeError!,
              style: TextStyle(
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : Colors.grey.shade600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: widget.onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_offer_outlined,
              size: 64,
              color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No Promo Codes Available',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF1A1A1A),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later for exciting offers and discounts!',
              style: TextStyle(
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : Colors.grey.shade600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TextField(
        onChanged: widget.onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search promo codes...',
          prefixIcon: Icon(
            Icons.search,
            color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade600,
          ),
          filled: true,
          fillColor: isDarkMode ? AppThemes.darkCard : Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        style: TextStyle(
          color:
              isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
        ),
      ),
    );
  }

  Widget _buildNoSearchResults(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No matching promo codes found',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF1A1A1A),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: TextStyle(
                color: isDarkMode
                    ? AppThemes.darkTextSecondary
                    : Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromoCodesList(List<PromoCode> promoCodes, bool isDarkMode,
      Color primaryColor, Color secondaryColor) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: promoCodes.length,
      itemBuilder: (context, index) {
        final promo = promoCodes[index];
        return _buildPromoCodeCard(
            promo, isDarkMode, primaryColor, secondaryColor);
      },
    );
  }

  Widget _buildPromoCodeCard(PromoCode promo, bool isDarkMode,
      Color primaryColor, Color secondaryColor) {
    // Check if current amount meets minimum requirement
    final bool canSelect = widget.currentAmount >= promo.minimumAmountApplicable;
    final bool isDisabled = !canSelect && promo.minimumAmountApplicable > 0;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canSelect 
              ? () => widget.onPromoSelected(promo)
              : () => _showMinimumAmountWarning(promo.minimumAmountApplicable),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isDisabled 
                  ? (isDarkMode ? AppThemes.darkCard.withValues(alpha: 0.5) : Colors.grey.shade100)
                  : (isDarkMode ? AppThemes.darkCard : Colors.white),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isDisabled
                    ? Colors.grey.withValues(alpha: 0.3)
                    : primaryColor.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: isDisabled ? null : [
                BoxShadow(
                  color: (isDarkMode ? Colors.black : Colors.grey)
                      .withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row with code and savings badge
                Row(
                  children: [
                    // Promo code with icon
                    Expanded(
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.local_offer,
                              color: primaryColor,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  promo.code.toUpperCase(),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                  ),
                                ),
                                if (promo.minimumAmountApplicable > 0)
                                  Text(
                                    'Min. ₹${promo.minimumAmountApplicable}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Savings badge or minimum amount indicator
                    if (isDisabled)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.orange,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 12,
                              color: Colors.orange.shade700,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Add ₹${(promo.minimumAmountApplicable - widget.currentAmount).toInt()}',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.orange.shade700,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [primaryColor, secondaryColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'SAVE ₹${promo.credits}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  promo.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade700,
                    height: 1.3,
                  ),
                ),

                const SizedBox(height: 12),

                // Footer with copy button and expiry
                Row(
                  children: [
                    // Copy code button - always available
                    GestureDetector(
                      onTap: () => _copyPromoCode(promo.code),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.copy,
                              size: 12,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Copy',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Expiry date
                    if (promo.endDate.isNotEmpty)
                      Text(
                        'Expires: ${_formatDate(promo.endDate)}',
                        style: TextStyle(
                          fontSize: 11,
                          color: isDarkMode
                              ? AppThemes.darkTextSecondary
                              : Colors.grey.shade500,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  void _copyPromoCode(String code) {
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Promo code "$code" copied to clipboard'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Show warning when user tries to select promo code without meeting minimum amount
  void _showMinimumAmountWarning(int minimumAmount) {
    final amountNeeded = minimumAmount - widget.currentAmount.toInt();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Minimum Amount Required',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    'Add ₹$amountNeeded more to use this promo code (Min: ₹$minimumAmount)',
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        action: SnackBarAction(
          label: 'Set ₹$minimumAmount',
          textColor: Colors.white,
          onPressed: () {
            // Close the promo selection sheet and set the minimum amount
            Navigator.pop(context);
            // This will trigger the parent widget to update the amount
            // We can't directly access the parent's controller, so we'll use a callback approach
          },
        ),
      ),
    );
  }
}

// Celebration overlay widget for promo code success
class _CelebrationOverlay extends StatefulWidget {
  final VoidCallback onComplete;

  const _CelebrationOverlay({required this.onComplete});

  @override
  State<_CelebrationOverlay> createState() => _CelebrationOverlayState();
}

class _CelebrationOverlayState extends State<_CelebrationOverlay>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late List<_ConfettiParticle> _particles;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    // Generate confetti particles
    _particles = List.generate(20, (index) => _ConfettiParticle());

    _controller.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          widget.onComplete();
        }
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          children: [
            // Confetti particles
            ..._particles.map((particle) => Positioned(
                  left: particle.x * MediaQuery.of(context).size.width,
                  top: particle.y *
                      MediaQuery.of(context).size.height *
                      _controller.value,
                  child: Transform.rotate(
                    angle: _rotationAnimation.value * particle.rotation,
                    child: Container(
                      width: particle.size,
                      height: particle.size,
                      decoration: BoxDecoration(
                        color: particle.color,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                )),

            // Success icon
            Center(
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 60,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Confetti particle class
class _ConfettiParticle {
  final double x;
  final double y;
  final double size;
  final Color color;
  final double rotation;

  _ConfettiParticle()
      : x = math.Random().nextDouble(),
        y = math.Random().nextDouble() * 0.3,
        size = math.Random().nextDouble() * 8 + 4,
        color = [
          Colors.red,
          Colors.blue,
          Colors.green,
          Colors.yellow,
          Colors.purple,
          Colors.orange,
        ][math.Random().nextInt(6)],
        rotation = math.Random().nextDouble() * 4;
}
