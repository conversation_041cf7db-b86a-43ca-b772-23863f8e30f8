import 'dart:async';
import 'dart:io';
import 'package:flutter/widgets.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

/// Connection status enumeration
enum ConnectionStatus {
  connected,
  disconnected,
  poor,
  unknown,
}

/// Connection quality enumeration
enum ConnectionQuality {
  excellent, // < 100ms response time
  good, // 100-300ms response time
  poor, // 300-1000ms response time
  bad, // > 1000ms response time
  unknown,
}

/// Network error type enumeration for better error handling
enum NetworkErrorType {
  timeout,
  dnsFailure,
  connectionRefused,
  networkUnreachable,
  temporaryFailure,
  unknown,
}

/// Connectivity service with debouncing and robust error handling
/// Follows industry best practices to prevent false positives from brief network interruptions
/// Includes app lifecycle management to handle background/foreground transitions
class ConnectivityService with WidgetsBindingObserver {
  // Singleton pattern
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  // Core connectivity components
  final _connectivity = Connectivity();
  late final InternetConnectionChecker _internetChecker;

  // Stream controllers
  final _connectionStatusController =
      StreamController<ConnectionStatus>.broadcast();
  final _connectionQualityController =
      StreamController<ConnectionQuality>.broadcast();

  // Public streams
  Stream<ConnectionStatus> get connectionStatus =>
      _connectionStatusController.stream;
  Stream<ConnectionQuality> get connectionQuality =>
      _connectionQualityController.stream;

  // Current status
  ConnectionStatus _currentStatus = ConnectionStatus.connected;
  ConnectionQuality _currentQuality = ConnectionQuality.good;

  ConnectionStatus get currentStatus => _currentStatus;
  ConnectionQuality get currentQuality => _currentQuality;
  bool get hasConnection => _currentStatus == ConnectionStatus.connected;
  bool get hasGoodConnection =>
      _currentStatus == ConnectionStatus.connected &&
      (_currentQuality == ConnectionQuality.excellent ||
          _currentQuality == ConnectionQuality.good);

  // Debouncing and timing
  Timer? _debounceTimer;
  Timer? _qualityCheckTimer;
  Timer? _periodicCheckTimer;
  DateTime? _lastConnectivityChange;

  // Configuration constants
  static const Duration _debounceDelay =
      Duration(seconds: 4); // 4 second debounce
  static const Duration _qualityCheckInterval =
      Duration(seconds: 30); // Quality check every 30s
  static const Duration _connectionTimeout =
      Duration(seconds: 8); // Timeout for connection tests
  static const Duration _periodicCheckInterval =
      Duration(minutes: 2); // Periodic validation every 2 minutes
  static const Duration _fastConnectivityTimeout =
      Duration(seconds: 3); // Fast timeout for immediate checks
  static const Duration _dnsLookupTimeout =
      Duration(seconds: 2); // Timeout for DNS lookups
  static const Duration _socketConnectionTimeout =
      Duration(seconds: 2); // Timeout for socket connections
  static const int _maxRetryAttempts = 3;

  // Subscription management
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // State tracking
  bool _isInitialized = false;
  bool _isDisposed = false;
  int _consecutiveFailures = 0;

  // App lifecycle tracking
  bool _isAppInBackground = false;
  DateTime? _backgroundTime;
  DateTime? _foregroundTime;

  /// Initialize the connectivity service
  void initialize() {
    if (_isInitialized || _isDisposed) return;

    debugPrint('🌐 CONNECTIVITY: Initializing with enhanced configuration...');

    // Register app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Configure InternetConnectionChecker with multiple reliable endpoints
    _internetChecker = InternetConnectionChecker.createInstance(
      checkTimeout: const Duration(seconds: 3), // Faster timeout for better UX
      checkInterval: const Duration(seconds: 10), // More frequent checks
    );

    // Add multiple reliable endpoints for better connectivity detection
    _internetChecker.addresses = [
      AddressCheckOptions(
        address: InternetAddress('*******', type: InternetAddressType.IPv4),
        port: 53,
        timeout: const Duration(seconds: 2),
      ),
      AddressCheckOptions(
        address: InternetAddress('*******', type: InternetAddressType.IPv4),
        port: 53,
        timeout: const Duration(seconds: 2),
      ),
      AddressCheckOptions(
        address:
            InternetAddress('**************', type: InternetAddressType.IPv4),
        port: 53,
        timeout: const Duration(seconds: 2),
      ),
    ];

    debugPrint(
        '🌐 CONNECTIVITY: Configured ${_internetChecker.addresses.length} endpoints for connectivity checking');

    // Listen to connectivity changes with debouncing
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _handleConnectivityChange,
      onError: (error) {
        debugPrint('🌐 CONNECTIVITY: Error in connectivity stream: $error');
        _handleConnectionError(error);
      },
    );

    // Start periodic quality monitoring
    _startQualityMonitoring();

    // Start periodic validation checks
    _startPeriodicValidation();

    // Perform initial comprehensive check
    _performInitialCheck();

    _isInitialized = true;
    debugPrint('🌐 CONNECTIVITY: Initialization complete');
  }

  /// Handle app lifecycle state changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    debugPrint('🌐 CONNECTIVITY: App lifecycle state changed to: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
      case AppLifecycleState.inactive:
        // App is inactive but still visible (e.g., during phone call)
        debugPrint('🌐 CONNECTIVITY: App became inactive');
        break;
    }
  }

  /// Handle app resumed from background
  void _handleAppResumed() {
    debugPrint('🌐 CONNECTIVITY: App resumed from background');

    _foregroundTime = DateTime.now();
    final wasInBackground = _isAppInBackground;
    _isAppInBackground = false;

    if (wasInBackground && _backgroundTime != null) {
      final backgroundDuration = _foregroundTime!.difference(_backgroundTime!);
      debugPrint(
          '🌐 CONNECTIVITY: App was in background for ${backgroundDuration.inMinutes} minutes');

      // If app was in background for more than 5 minutes, force connectivity re-check
      if (backgroundDuration.inMinutes >= 5) {
        debugPrint(
            '🌐 CONNECTIVITY: Long background period detected, forcing connectivity re-verification');
        _forceConnectivityRecheck();
      } else {
        debugPrint(
            '🌐 CONNECTIVITY: Short background period, performing quick connectivity check');
        _performConnectivityCheck(immediate: true);
      }
    } else {
      debugPrint(
          '🌐 CONNECTIVITY: App resumed, performing standard connectivity check');
      _performConnectivityCheck(immediate: true);
    }
  }

  /// Handle app paused (going to background)
  void _handleAppPaused() {
    debugPrint('🌐 CONNECTIVITY: App paused (going to background)');

    _isAppInBackground = true;
    _backgroundTime = DateTime.now();

    // Cancel any pending timers to save battery
    _debounceTimer?.cancel();

    debugPrint(
        '🌐 CONNECTIVITY: Background state set, timers paused for battery optimization');
  }

  /// Handle app detached
  void _handleAppDetached() {
    debugPrint('🌐 CONNECTIVITY: App detached');
    _isAppInBackground = true;
  }

  /// Handle app hidden
  void _handleAppHidden() {
    debugPrint('🌐 CONNECTIVITY: App hidden');
    _isAppInBackground = true;
  }

  /// Force comprehensive connectivity re-check after long background periods
  Future<void> _forceConnectivityRecheck() async {
    debugPrint(
        '🌐 CONNECTIVITY: Starting forced connectivity re-check after background period');

    try {
      // Reset consecutive failures counter
      _consecutiveFailures = 0;

      // Stop and restart periodic timers
      _qualityCheckTimer?.cancel();
      _periodicCheckTimer?.cancel();

      // Perform comprehensive connectivity verification
      final hasConnection = await verifyInternetConnectivity();

      if (hasConnection) {
        debugPrint(
            '🌐 CONNECTIVITY: Forced re-check successful - connection verified');
        final quality = await _measureConnectionQuality();
        _updateConnectionStatus(ConnectionStatus.connected, quality);
      } else {
        debugPrint(
            '🌐 CONNECTIVITY: Forced re-check failed - no connection detected');
        _updateConnectionStatus(
            ConnectionStatus.disconnected, ConnectionQuality.unknown);
      }

      // Restart periodic monitoring
      _startQualityMonitoring();
      _startPeriodicValidation();
    } catch (e) {
      debugPrint('🌐 CONNECTIVITY: Error during forced re-check: $e');
      _handleConnectionError(e);
    }
  }

  /// Handle connectivity changes with debouncing
  void _handleConnectivityChange(List<ConnectivityResult> results) {
    _lastConnectivityChange = DateTime.now();

    debugPrint(
        '🌐 CONNECTIVITY: Raw connectivity change: $results at $_lastConnectivityChange');

    // Log detailed connectivity information
    _logConnectivityDetails(results);

    // Cancel existing debounce timer
    _debounceTimer?.cancel();

    // Check if we have any connection according to the system
    final hasAnyConnection =
        results.any((result) => result != ConnectivityResult.none);

    if (hasAnyConnection) {
      // If we have a connection according to the system, check immediately for good news
      // Use shorter debounce if app was recently in background to prevent false negatives
      if (_isAppInBackground ||
          (_backgroundTime != null &&
              DateTime.now().difference(_backgroundTime!).inMinutes < 2)) {
        debugPrint(
            '🌐 CONNECTIVITY: Recent background activity detected, using immediate check');
        _performConnectivityCheck(immediate: true);
      } else {
        _performConnectivityCheck(immediate: true);
      }
    } else {
      // For disconnection, use debouncing to avoid false positives
      // But reduce debounce time if app was recently in background
      final debounceDelay = (_isAppInBackground ||
              (_backgroundTime != null &&
                  DateTime.now().difference(_backgroundTime!).inMinutes < 2))
          ? const Duration(seconds: 2) // Shorter delay after background
          : _debounceDelay; // Normal delay

      _debounceTimer = Timer(debounceDelay, () {
        debugPrint('🌐 CONNECTIVITY: Debounce timer expired, performing check');
        _performConnectivityCheck();
      });
    }
  }

  /// Log detailed connectivity information for debugging
  void _logConnectivityDetails(List<ConnectivityResult> results) {
    for (final result in results) {
      switch (result) {
        case ConnectivityResult.wifi:
          debugPrint('🌐 CONNECTIVITY: WiFi connection detected');
          break;
        case ConnectivityResult.mobile:
          debugPrint('🌐 CONNECTIVITY: Mobile data connection detected');
          break;
        case ConnectivityResult.ethernet:
          debugPrint('🌐 CONNECTIVITY: Ethernet connection detected');
          break;
        case ConnectivityResult.vpn:
          debugPrint('🌐 CONNECTIVITY: VPN connection detected');
          break;
        case ConnectivityResult.bluetooth:
          debugPrint('🌐 CONNECTIVITY: Bluetooth connection detected');
          break;
        case ConnectivityResult.other:
          debugPrint('🌐 CONNECTIVITY: Other connection type detected');
          break;
        case ConnectivityResult.none:
          debugPrint('🌐 CONNECTIVITY: No connection detected');
          break;
      }
    }
  }

  /// Perform initial comprehensive connectivity check
  void _performInitialCheck() async {
    debugPrint('🌐 CONNECTIVITY: Performing initial connectivity check');
    await _performConnectivityCheck(immediate: true);
  }

  /// Start periodic quality monitoring
  void _startQualityMonitoring() {
    _qualityCheckTimer?.cancel();
    _qualityCheckTimer = Timer.periodic(_qualityCheckInterval, (_) {
      if (!_isDisposed && _currentStatus == ConnectionStatus.connected) {
        _checkConnectionQuality();
      }
    });
  }

  /// Start periodic validation checks
  void _startPeriodicValidation() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = Timer.periodic(_periodicCheckInterval, (_) {
      if (!_isDisposed) {
        debugPrint('🌐 CONNECTIVITY: Performing periodic validation');
        _performConnectivityCheck();
      }
    });
  }

  /// Perform comprehensive connectivity check
  Future<void> _performConnectivityCheck({bool immediate = false}) async {
    if (_isDisposed) return;

    try {
      debugPrint(
          '🌐 CONNECTIVITY: Starting connectivity check (immediate: $immediate)');

      // Step 1: Check system connectivity
      final connectivityResults = await _connectivity.checkConnectivity();
      debugPrint('🌐 CONNECTIVITY: System connectivity: $connectivityResults');

      // Check if we have any connection
      final hasAnyConnection = connectivityResults
          .any((result) => result != ConnectivityResult.none);

      if (!hasAnyConnection) {
        _updateConnectionStatus(
            ConnectionStatus.disconnected, ConnectionQuality.unknown);
        _consecutiveFailures++;
        return;
      }

      // Step 2: Check actual internet connectivity with timeout
      bool hasInternet = false;
      try {
        hasInternet = await _internetChecker.hasConnection
            .timeout(_connectionTimeout, onTimeout: () => false);
        debugPrint('🌐 CONNECTIVITY: Internet check result: $hasInternet');
      } catch (e) {
        debugPrint('🌐 CONNECTIVITY: Internet check failed: $e');
        hasInternet = false;
      }

      if (!hasInternet) {
        // Step 3: Fallback check with direct DNS lookup
        hasInternet = await _performFallbackConnectivityCheck();
      }

      if (hasInternet) {
        _consecutiveFailures = 0;
        // Check connection quality
        final quality = await _measureConnectionQuality();
        _updateConnectionStatus(ConnectionStatus.connected, quality);
      } else {
        _consecutiveFailures++;
        _updateConnectionStatus(
            ConnectionStatus.disconnected, ConnectionQuality.unknown);
      }
    } catch (e) {
      debugPrint('🌐 CONNECTIVITY: Error in connectivity check: $e');
      _handleConnectionError(e);
    }
  }

  /// Enhanced fallback connectivity check using multiple DNS servers and endpoints
  Future<bool> _performFallbackConnectivityCheck() async {
    debugPrint(
        '🌐 CONNECTIVITY: Performing enhanced fallback connectivity check');

    // List of reliable endpoints to test
    final testEndpoints = [
      'google.com',
      'cloudflare.com',
      'microsoft.com',
      'amazon.com',
    ];

    // List of DNS servers to test direct connectivity
    final dnsServers = [
      '*******', // Google DNS
      '*******', // Cloudflare DNS
      '**************', // OpenDNS
    ];

    // Test 1: Try DNS lookups for reliable domains
    for (final endpoint in testEndpoints) {
      try {
        final stopwatch = Stopwatch()..start();
        final result = await InternetAddress.lookup(endpoint)
            .timeout(_dnsLookupTimeout, onTimeout: () => []);
        stopwatch.stop();

        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          debugPrint(
              '🌐 CONNECTIVITY: Fallback DNS lookup success for $endpoint (${stopwatch.elapsedMilliseconds}ms)');
          return true;
        }
      } catch (e) {
        debugPrint('🌐 CONNECTIVITY: DNS lookup failed for $endpoint: $e');
        continue;
      }
    }

    // Test 2: Try direct socket connections to DNS servers
    for (final dnsServer in dnsServers) {
      try {
        final stopwatch = Stopwatch()..start();
        final socket = await Socket.connect(
          dnsServer,
          53,
          timeout: _socketConnectionTimeout,
        );
        await socket.close();
        stopwatch.stop();

        debugPrint(
            '🌐 CONNECTIVITY: Direct socket connection success to $dnsServer (${stopwatch.elapsedMilliseconds}ms)');
        return true;
      } catch (e) {
        debugPrint(
            '🌐 CONNECTIVITY: Socket connection failed to $dnsServer: $e');
        continue;
      }
    }

    debugPrint('🌐 CONNECTIVITY: All fallback connectivity checks failed');
    return false;
  }

  /// Enhanced connection quality measurement using multiple endpoints
  Future<ConnectionQuality> _measureConnectionQuality() async {
    try {
      final testEndpoints = ['google.com', 'cloudflare.com', 'microsoft.com'];
      final responseTimes = <int>[];

      // Test multiple endpoints and calculate average response time
      for (final endpoint in testEndpoints) {
        try {
          final stopwatch = Stopwatch()..start();
          final result = await InternetAddress.lookup(endpoint)
              .timeout(_dnsLookupTimeout, onTimeout: () => []);
          stopwatch.stop();

          if (result.isNotEmpty) {
            responseTimes.add(stopwatch.elapsedMilliseconds);
            debugPrint(
                '🌐 CONNECTIVITY: Quality test for $endpoint: ${stopwatch.elapsedMilliseconds}ms');
          }
        } catch (e) {
          debugPrint('🌐 CONNECTIVITY: Quality test failed for $endpoint: $e');
          continue;
        }
      }

      if (responseTimes.isEmpty) {
        return ConnectionQuality.unknown;
      }

      // Calculate average response time
      final averageResponseTime =
          responseTimes.reduce((a, b) => a + b) / responseTimes.length;
      debugPrint(
          '🌐 CONNECTIVITY: Average response time: ${averageResponseTime.toStringAsFixed(1)}ms');

      // Determine quality based on average response time
      if (averageResponseTime < 100) {
        return ConnectionQuality.excellent;
      } else if (averageResponseTime < 300) {
        return ConnectionQuality.good;
      } else if (averageResponseTime < 1000) {
        return ConnectionQuality.poor;
      } else {
        return ConnectionQuality.bad;
      }
    } catch (e) {
      debugPrint('🌐 CONNECTIVITY: Quality check failed: $e');
      return ConnectionQuality.unknown;
    }
  }

  /// Check connection quality periodically
  void _checkConnectionQuality() async {
    if (_currentStatus == ConnectionStatus.connected) {
      final quality = await _measureConnectionQuality();
      if (quality != _currentQuality) {
        _currentQuality = quality;
        _connectionQualityController.add(quality);
        debugPrint('🌐 CONNECTIVITY: Quality updated to: $quality');
      }
    }
  }

  /// Update connection status and notify listeners
  void _updateConnectionStatus(
      ConnectionStatus status, ConnectionQuality quality) {
    final statusChanged = _currentStatus != status;
    final qualityChanged = _currentQuality != quality;

    if (statusChanged || qualityChanged) {
      _currentStatus = status;
      _currentQuality = quality;

      if (statusChanged) {
        _connectionStatusController.add(status);
        debugPrint('🌐 CONNECTIVITY: Status changed to: $status');
      }

      if (qualityChanged) {
        _connectionQualityController.add(quality);
        debugPrint('🌐 CONNECTIVITY: Quality changed to: $quality');
      }
    }
  }

  /// Handle connection errors with detailed error analysis
  void _handleConnectionError(dynamic error) {
    debugPrint('🌐 CONNECTIVITY: Handling error: $error');
    _consecutiveFailures++;

    // Analyze error type for better handling
    final errorType = _analyzeError(error);
    debugPrint('🌐 CONNECTIVITY: Error type: $errorType');

    // Determine appropriate status based on error type
    ConnectionStatus status;
    ConnectionQuality quality = ConnectionQuality.unknown;

    switch (errorType) {
      case NetworkErrorType.timeout:
        status = ConnectionStatus.poor;
        quality = ConnectionQuality.bad;
        break;
      case NetworkErrorType.dnsFailure:
      case NetworkErrorType.connectionRefused:
      case NetworkErrorType.networkUnreachable:
        status = ConnectionStatus.disconnected;
        break;
      case NetworkErrorType.temporaryFailure:
        status = ConnectionStatus.poor;
        quality = ConnectionQuality.poor;
        break;
      case NetworkErrorType.unknown:
        // If we have too many consecutive failures, mark as disconnected
        if (_consecutiveFailures >= _maxRetryAttempts) {
          status = ConnectionStatus.disconnected;
        } else {
          status = ConnectionStatus.unknown;
        }
        break;
    }

    _updateConnectionStatus(status, quality);
  }

  /// Analyze error to determine its type
  NetworkErrorType _analyzeError(dynamic error) {
    if (error == null) return NetworkErrorType.unknown;

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout') || errorString.contains('timed out')) {
      return NetworkErrorType.timeout;
    }

    if (errorString.contains('dns') ||
        errorString.contains('host lookup failed') ||
        errorString.contains('temporary failure in name resolution')) {
      return NetworkErrorType.dnsFailure;
    }

    if (errorString.contains('connection refused') ||
        errorString.contains('connection reset') ||
        errorString.contains('connection aborted')) {
      return NetworkErrorType.connectionRefused;
    }

    if (errorString.contains('network is unreachable') ||
        errorString.contains('no route to host') ||
        errorString.contains('unreachable')) {
      return NetworkErrorType.networkUnreachable;
    }

    if (errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504') ||
        errorString.contains('temporary')) {
      return NetworkErrorType.temporaryFailure;
    }

    // Check for specific exception types
    if (error is SocketException) {
      return NetworkErrorType.connectionRefused;
    }

    if (error is TimeoutException) {
      return NetworkErrorType.timeout;
    }

    return NetworkErrorType.unknown;
  }

  /// Manually trigger connectivity check
  Future<bool> checkConnectionManually() async {
    debugPrint('🌐 CONNECTIVITY: Manual connectivity check requested');
    await _performConnectivityCheck(immediate: true);
    return hasConnection;
  }

  /// Manually trigger background recovery check
  /// Useful for testing or when you suspect connectivity state is stale
  Future<bool> checkConnectionAfterBackground() async {
    debugPrint('🌐 CONNECTIVITY: Manual background recovery check requested');
    await _forceConnectivityRecheck();
    return hasConnection;
  }

  /// Comprehensive connectivity verification for critical operations
  /// Uses multiple verification methods to ensure accurate connectivity status
  Future<bool> verifyInternetConnectivity() async {
    debugPrint(
        '🌐 CONNECTIVITY: Performing comprehensive connectivity verification');

    try {
      // Step 1: Quick system connectivity check
      final connectivityResults = await _connectivity.checkConnectivity();
      final hasAnyConnection = connectivityResults
          .any((result) => result != ConnectivityResult.none);

      if (!hasAnyConnection) {
        debugPrint('🌐 CONNECTIVITY: No network interface available');
        return false;
      }

      // Step 2: Primary internet checker
      bool hasInternet = false;
      try {
        hasInternet = await _internetChecker.hasConnection
            .timeout(_fastConnectivityTimeout, onTimeout: () => false);
        debugPrint('🌐 CONNECTIVITY: Primary internet check: $hasInternet');
      } catch (e) {
        debugPrint('🌐 CONNECTIVITY: Primary internet check failed: $e');
      }

      // Step 3: If primary check fails, use enhanced fallback
      if (!hasInternet) {
        hasInternet = await _performFallbackConnectivityCheck();
        debugPrint('🌐 CONNECTIVITY: Fallback check result: $hasInternet');
      }

      // Step 4: If still no connection, try one more quick DNS test
      if (!hasInternet) {
        try {
          final result = await InternetAddress.lookup('*******')
              .timeout(_dnsLookupTimeout, onTimeout: () => []);
          hasInternet = result.isNotEmpty;
          debugPrint('🌐 CONNECTIVITY: Final DNS test: $hasInternet');
        } catch (e) {
          debugPrint('🌐 CONNECTIVITY: Final DNS test failed: $e');
        }
      }

      debugPrint(
          '🌐 CONNECTIVITY: Comprehensive verification result: $hasInternet');
      return hasInternet;
    } catch (e) {
      debugPrint('🌐 CONNECTIVITY: Comprehensive verification error: $e');
      return false;
    }
  }

  /// Execute station pagination API call with connectivity handling
  /// This method provides dynamic URL construction with location and power output parameters
  Future<T> executeStationPaginationCall<T>(
    Future<T> Function() apiCall, {
    String? errorMessage,
    int maxRetries = 3,
  }) async {
    debugPrint('🌐 CONNECTIVITY: Executing station pagination API call');

    // Check connectivity before making the call
    final isConnected = await checkConnectionManually();
    if (!isConnected) {
      throw Exception(
          'No internet connection available for station pagination');
    }

    int attempts = 0;
    Exception? lastException;

    while (attempts < maxRetries) {
      try {
        attempts++;
        debugPrint(
            '🌐 CONNECTIVITY: Station pagination attempt $attempts/$maxRetries');

        final result = await apiCall();
        debugPrint('🌐 CONNECTIVITY: Station pagination call successful');
        return result;
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        debugPrint(
            '🌐 CONNECTIVITY: Station pagination attempt $attempts failed: $e');

        if (attempts < maxRetries) {
          // Check connectivity before retrying
          final stillConnected = await checkConnectionManually();
          if (!stillConnected) {
            throw Exception('Connection lost during station pagination retry');
          }

          // Exponential backoff delay
          final delay = Duration(milliseconds: 1000 * attempts);
          debugPrint(
              '🌐 CONNECTIVITY: Retrying station pagination in ${delay.inMilliseconds}ms');
          await Future.delayed(delay);
        }
      }
    }

    // All retries exhausted
    final finalError = errorMessage ??
        'Failed to load station data after $maxRetries attempts';
    debugPrint(
        '🌐 CONNECTIVITY: Station pagination failed after all retries: $finalError');
    throw lastException ?? Exception(finalError);
  }

  /// Get connection status as boolean for backward compatibility
  bool get hasConnectionLegacy => hasConnection;

  /// Get current connection types
  Future<List<ConnectivityResult>> getCurrentConnectionTypes() async {
    try {
      final results = await _connectivity.checkConnectivity();
      debugPrint('🌐 CONNECTIVITY: Current connection types: $results');
      return results;
    } catch (e) {
      debugPrint('🌐 CONNECTIVITY: Error getting connection types: $e');
      return [ConnectivityResult.none];
    }
  }

  /// Check if specific connection type is available
  Future<bool> hasConnectionType(ConnectivityResult type) async {
    final results = await getCurrentConnectionTypes();
    return results.contains(type);
  }

  /// Check if WiFi is available
  Future<bool> hasWiFi() async =>
      await hasConnectionType(ConnectivityResult.wifi);

  /// Check if mobile data is available
  Future<bool> hasMobileData() async =>
      await hasConnectionType(ConnectivityResult.mobile);

  /// Check if ethernet is available
  Future<bool> hasEthernet() async =>
      await hasConnectionType(ConnectivityResult.ethernet);

  /// Check if VPN is active
  Future<bool> hasVPN() async =>
      await hasConnectionType(ConnectivityResult.vpn);

  /// Get a human-readable description of current connection types
  Future<String> getConnectionDescription() async {
    final results = await getCurrentConnectionTypes();

    if (results.isEmpty || results.contains(ConnectivityResult.none)) {
      return 'No connection';
    }

    final descriptions = <String>[];
    for (final result in results) {
      switch (result) {
        case ConnectivityResult.wifi:
          descriptions.add('WiFi');
          break;
        case ConnectivityResult.mobile:
          descriptions.add('Mobile Data');
          break;
        case ConnectivityResult.ethernet:
          descriptions.add('Ethernet');
          break;
        case ConnectivityResult.vpn:
          descriptions.add('VPN');
          break;
        case ConnectivityResult.bluetooth:
          descriptions.add('Bluetooth');
          break;
        case ConnectivityResult.other:
          descriptions.add('Other');
          break;
        case ConnectivityResult.none:
          // Skip none results
          break;
      }
    }

    return descriptions.isEmpty
        ? 'Unknown connection'
        : descriptions.join(', ');
  }

  /// Dispose of all resources
  void dispose() {
    if (_isDisposed) return;

    debugPrint('🌐 CONNECTIVITY: Disposing resources');

    _isDisposed = true;

    // Remove app lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    _debounceTimer?.cancel();
    _qualityCheckTimer?.cancel();
    _periodicCheckTimer?.cancel();
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
    _connectionQualityController.close();
  }
}
