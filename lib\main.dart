import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ecoplug/firebase_options.dart';
import 'package:ecoplug/services/fcm_service.dart';
import 'package:ecoplug/services/fcm_subscription_service.dart';
import 'package:ecoplug/services/messaging_service.dart';
import 'package:ecoplug/services/unified_notification_service.dart';
import 'package:ecoplug/services/auth_notification_service.dart';
import 'package:ecoplug/services/charging_session_notification_manager.dart';
import 'package:ecoplug/services/local_notification_manager.dart';
import 'package:ecoplug/services/persistent_charging_service.dart';
import 'package:ecoplug/services/deep_link_service.dart';
import 'package:ecoplug/screens/auth/auth_screen.dart';
import 'package:ecoplug/screens/splash/splash_screen.dart';
import 'package:ecoplug/widgets/navigation_bar.dart';
import 'package:ecoplug/screens/Trip/trip_page.dart';
import 'package:ecoplug/screens/Profile/Profilescreen/profile_screen_riverpod.dart';
import 'package:ecoplug/screens/dashboard/dashboard_horizontal_cards.dart';
import 'screens/station/station_list_page.dart';
import 'screens/station/station_details_page_fixed.dart';
import 'screens/wallet/wallet_screen.dart';
import 'screens/charging/charging_history_page.dart';
import 'screens/charging/active_sessions_screen.dart';
import 'screens/charging_session_screen.dart';
import 'screens/error/connectivity_error_page.dart';
import 'services/notification_navigation_service.dart';
import 'services/connectivity_monitor.dart';
import 'utils/app_themes.dart';
import 'utils/notification_test_utility.dart'; // Import notification test utility
import 'providers/provider_observer.dart'; // Import the provider observer
import 'providers/providers.dart'; // Import all providers
import 'utils/route_observer.dart'; // Import route observer
import 'debug/fcm_debug_widget.dart'; // Import FCM debug widget

// Background message handler for FCM following tutorial pattern
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase for background handler
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  debugPrint('🔔 ===== FCM BACKGROUND MESSAGE RECEIVED =====');
  debugPrint('🔔 Message ID: ${message.messageId}');
  debugPrint('🔔 From: ${message.from}');
  debugPrint('🔔 Data: ${message.data}');

  if (message.notification != null) {
    debugPrint('🔔 Title: ${message.notification!.title}');
    debugPrint('🔔 Body: ${message.notification!.body}');
  }

  // Handle background message based on type
  if (message.data.containsKey('type')) {
    switch (message.data['type']) {
      case 'charging':
        debugPrint('🔔 Background charging notification received');
        // Handle charging notification in background
        break;
      case 'station':
        debugPrint('🔔 Background station notification received');
        // Handle station notification in background
        break;
      default:
        debugPrint(
            '🔔 Unknown background notification type: ${message.data['type']}');
    }
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first (required for other services)
  try {
    final totalStopwatch = Stopwatch()..start();
    debugPrint(
        '🚀 ===== STARTING SEQUENTIAL SERVICE INITIALIZATION FOR DEBUGGING =====');

    final firebaseStopwatch = Stopwatch()..start();
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    firebaseStopwatch.stop();
    debugPrint(
        '✅ Firebase initialized successfully in ${firebaseStopwatch.elapsedMilliseconds}ms');

    // Set up background message handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // Helper function to time service initialization
    Future<void> timeService(
        String serviceName, Future<void> Function() initFunction) async {
      final stopwatch = Stopwatch()..start();
      debugPrint('🚀 Initializing $serviceName...');
      try {
        await initFunction();
        stopwatch.stop();
        debugPrint(
            '✅ $serviceName initialized successfully in ${stopwatch.elapsedMilliseconds}ms');
      } catch (e) {
        stopwatch.stop();
        debugPrint(
            '❌ $serviceName failed to initialize in ${stopwatch.elapsedMilliseconds}ms. Error: $e');
      }
    }

    // Sequentially initialize services to measure each one
    await timeService('LocalNotificationManager', () async {
      final localNotificationManager = LocalNotificationManager();
      await localNotificationManager.initialize();
    });

    await timeService('UnifiedNotificationService', () async {
      final notificationService = UnifiedNotificationService();
      await notificationService.initialize();
    });

    await timeService('AuthNotificationService', () async {
      final authNotificationService = AuthNotificationService();
      await authNotificationService.initialize();
    });

    await timeService('ChargingSessionNotificationManager', () async {
      final chargingNotificationManager = ChargingSessionNotificationManager();
      await chargingNotificationManager.initialize();
    });

    await timeService('FCMService', () async {
      final fcmService = FCMService();
      await fcmService.initialize();
    });

    await timeService('FCMSubscriptionService', () async {
      final fcmSubscriptionService = FCMSubscriptionService();
      await fcmSubscriptionService.initialize();
    });

    await timeService('MessagingService', () async {
      final messagingService = MessagingService();
      await messagingService.init();
    });

    await timeService('PersistentChargingService', () async {
      final persistentChargingService = PersistentChargingService();
      await persistentChargingService.initialize();
    });

    await timeService('DeepLinkService', () async {
      final deepLinkService = DeepLinkService();
      await deepLinkService.initialize(
        onOpenChargingSession: () {
          debugPrint('🔗 Deep link triggered: navigating to charging session');
        },
        onDeepLinkReceived: (data) {
          debugPrint('🔗 Deep link received: $data');
        },
      );
    });

    await timeService('NotificationTestUtility (Permissions)', () async {
      await NotificationTestUtility.initializePermissionsOnly();
    });

    totalStopwatch.stop();
    debugPrint(
        '🚀 ===== ALL SERVICES INITIALIZED SEQUENTIALLY IN ${totalStopwatch.elapsedMilliseconds}ms =====');
  } catch (e) {
    debugPrint('❌ Error initializing Firebase/Notifications: $e');
  }

  // Fix green line issues by ensuring proper rendering
  // Disable layout bounds visual debug rendering
  // Remove when issue is resolved
  debugDisableShadows = false;

  // Google Maps API key is configured in AndroidManifest.xml and AppDelegate.swift

  runApp(
    ProviderScope(
      // Wrap the app with ProviderScope
      observers: [LoggingProviderObserver()], // Add provider observer
      child: MyApp(),
    ),
  );
} // <-- Ensure main() properly closes here

class MyApp extends ConsumerWidget {
  // Change to ConsumerWidget
  const MyApp({super.key}); // Remove apiBridge parameter

  // Global navigator key for notification navigation
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add WidgetRef ref
    // Access themeMode using Riverpod
    final themeMode = ref.watch(themeNotifierProvider);

    // Initialize notification navigation service
    final navigationService = NotificationNavigationService();
    navigationService.initialize(navigatorKey);

    // Initialize connectivity monitor
    ConnectivityMonitor.initialize(navigatorKey);

    return MaterialApp(
      title: 'Ecoplug',
      debugShowCheckedModeBanner: false,
      theme: AppThemes.lightTheme,
      darkTheme: AppThemes.darkTheme,
      themeMode: themeMode, // Use Riverpod themeMode here
      navigatorKey:
          navigatorKey, // Add navigator key for notification navigation
      navigatorObservers: [routeObserver], // Add route observer
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/auth': (context) => const AuthScreen(),
        '/dashboard': (context) =>
            const MainNavigation(), // Main navigation with horizontal cards as home tab
        '/dashboard-horizontal': (context) =>
            const DashboardHorizontalCards(), // Direct access to horizontal cards dashboard
        '/stationList': (context) =>
            const StationListPage(), // This will be replaced by direct navigation
        '/wallet': (context) => const WalletPage(),
        '/trip': (context) => const TripPage(),
        '/profile': (context) => const ProfileScreenRiverpod(),
        '/charging-history': (context) => const ChargingHistoryPage(),
        '/active-sessions': (context) => const ActiveSessionsScreen(),
        '/fcm-debug': (context) => const FCMDebugWidget(), // FCM debug route
        '/connectivity-error': (context) {
          // Handle connectivity error route with optional arguments
          final args = ModalRoute.of(context)?.settings.arguments
              as Map<String, dynamic>?;
          return ConnectivityErrorPage(
            errorMessage: args?['error_message'] as String?,
          );
        },
        '/charging_session': (context) {
          // Handle charging session route with arguments
          final args = ModalRoute.of(context)?.settings.arguments
              as Map<String, dynamic>?;
          return ChargingSessionScreen(
            stationUid: args?['station_uid'] as String?,
            connectorId: args?['connector_id'] as String?,
            initialCharge: args?['charge_percentage'] as double? ?? 0.0,
            verifiedSessionData:
                args?['verified_session_data'] as Map<String, dynamic>?,
          );
        },
      },
      // Use onGenerateRoute for routes that need parameters
      onGenerateRoute: (settings) {
        if (settings.name == '/stationDetails') {
          // Extract the arguments
          final args = settings.arguments;

          if (args is Map<String, dynamic>) {
            // Check if we have a UID directly
            if (args.containsKey('uid') && args['uid'] is String) {
              final String uid = args['uid'] as String;
              if (uid.isNotEmpty) {
                return MaterialPageRoute(
                  builder: (context) {
                    if (uid.isEmpty) {
                      throw ArgumentError(
                          'UID cannot be empty when launching StationDetailsPage');
                    }
                    return StationDetailsPage(uid: uid);
                  },
                );
              }
            }

            // Extract UID from arguments directly
            // Extract UID from arguments
            final String uid = args['uid'] as String? ?? '';
            if (uid.isNotEmpty) {
              return MaterialPageRoute(
                builder: (context) {
                  return StationDetailsPage(uid: uid);
                },
              );
            } else {
              // Handle missing UID
              return MaterialPageRoute(
                builder: (context) => const Scaffold(
                  body: Center(
                    child: Text('Invalid station information'),
                  ),
                ),
              );
            }
          }
        }
        return null;
      },
    );
  }
}
