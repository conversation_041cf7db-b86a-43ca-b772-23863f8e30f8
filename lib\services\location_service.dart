import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Service for handling location-related functionality
class LocationService {
  // Cache the last known location
  static Position? _lastKnownLocation;
  static DateTime? _lastLocationTime;
  static bool _isLocationInitialized = false;

  // Callback for when permission is granted for the first time
  static Function()? _onPermissionGrantedCallback;

  /// Get the current location of the device with improved permission handling
  Future<Position?> getCurrentLocation() async {
    try {
      // Check if we have a recent location (less than 1 minute old)
      if (_lastKnownLocation != null &&
          _lastLocationTime != null &&
          DateTime.now().difference(_lastLocationTime!).inMinutes < 1) {
        debugPrint(
            '🗺️ LocationService: Using cached location (${DateTime.now().difference(_lastLocationTime!).inSeconds}s old)');
        return _lastKnownLocation;
      }

      debugPrint('🗺️ LocationService: Getting fresh location...');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ LocationService: Location services are disabled');
        return null;
      }

      // Enhanced permission handling with proper waiting for permission grant
      LocationPermission permission = await Geolocator.checkPermission();
      debugPrint('🗺️ LocationService: Current permission status: $permission');

      if (permission == LocationPermission.denied) {
        debugPrint('🗺️ LocationService: Requesting location permission...');
        permission = await Geolocator.requestPermission();
        debugPrint(
            '🗺️ LocationService: Permission request result: $permission');

        if (permission == LocationPermission.denied) {
          debugPrint('❌ LocationService: Location permissions denied by user');
          return null;
        }

        // If permission was just granted, add a small delay to ensure system processes it
        if (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always) {
          debugPrint(
              '✅ LocationService: Permission granted, waiting for system to process...');
          await Future.delayed(const Duration(milliseconds: 500));

          // Trigger callback for first-time permission grant
          _triggerPermissionGrantedCallback();
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint(
            '❌ LocationService: Location permissions permanently denied');
        return null;
      }

      debugPrint(
          '🗺️ LocationService: Getting current position with permission: $permission');

      // Get the current position with extended timeout for first-time users
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(
              seconds: 15), // Extended timeout for first-time permission grants
        ),
      );

      debugPrint(
          '✅ LocationService: Location obtained: ${position.latitude}, ${position.longitude}');

      // Cache the location
      _lastKnownLocation = position;
      _lastLocationTime = DateTime.now();
      _isLocationInitialized = true;

      return position;
    } catch (e) {
      debugPrint('❌ LocationService: Error getting current location: $e');
      return null;
    }
  }

  /// Get the last known location without requesting a new one
  Position? getLastKnownLocation() {
    return _lastKnownLocation;
  }

  /// Check if location has been initialized at least once
  bool isLocationInitialized() {
    return _isLocationInitialized;
  }

  /// Force location to be re-fetched on next request
  void invalidateLocation() {
    _isLocationInitialized = false;
    _lastKnownLocation = null;
    _lastLocationTime = null;
  }

  /// Set a callback to be triggered when location permission is granted for the first time
  static void setPermissionGrantedCallback(Function() callback) {
    _onPermissionGrantedCallback = callback;
  }

  /// Clear the permission granted callback
  static void clearPermissionGrantedCallback() {
    _onPermissionGrantedCallback = null;
  }

  /// Trigger the permission granted callback if set
  static void _triggerPermissionGrantedCallback() {
    if (_onPermissionGrantedCallback != null) {
      debugPrint(
          '🚀 LocationService: Triggering permission granted callback...');
      _onPermissionGrantedCallback!();
    }
  }

  /// Get location specifically for first-time app launches with enhanced permission handling
  /// This method is optimized for new users who are granting location permission for the first time
  Future<Position?> getCurrentLocationForFirstTime() async {
    try {
      debugPrint('🚀 LocationService: Getting location for first-time user...');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ LocationService: Location services are disabled');
        return null;
      }

      // Enhanced permission handling for first-time users
      LocationPermission permission = await Geolocator.checkPermission();
      debugPrint('🚀 LocationService: Initial permission status: $permission');

      if (permission == LocationPermission.denied) {
        debugPrint(
            '🚀 LocationService: Requesting location permission for first-time user...');
        permission = await Geolocator.requestPermission();
        debugPrint(
            '🚀 LocationService: Permission request result: $permission');

        if (permission == LocationPermission.denied) {
          debugPrint(
              '❌ LocationService: Location permissions denied by first-time user');
          return null;
        }

        // For first-time permission grants, wait longer for system to process
        if (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always) {
          debugPrint(
              '✅ LocationService: First-time permission granted, waiting for system to fully process...');
          await Future.delayed(const Duration(
              milliseconds: 1000)); // Longer delay for first-time users

          // Trigger callback for first-time permission grant
          _triggerPermissionGrantedCallback();
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint(
            '❌ LocationService: Location permissions permanently denied');
        return null;
      }

      debugPrint(
          '🚀 LocationService: Getting position for first-time user with permission: $permission');

      // Get position with extended timeout and multiple retry attempts for first-time users
      Position? position;
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          debugPrint(
              '🚀 LocationService: Attempt ${retryCount + 1}/$maxRetries to get location...');

          position = await Geolocator.getCurrentPosition(
            locationSettings: LocationSettings(
              accuracy: LocationAccuracy.high,
              timeLimit: Duration(
                  seconds:
                      20 + (retryCount * 5)), // Increasing timeout with retries
            ),
          );

          debugPrint(
              '✅ LocationService: First-time location obtained on attempt ${retryCount + 1}: ${position.latitude}, ${position.longitude}');
          break; // Success, exit the retry loop
        } catch (e) {
          debugPrint(
              '⚠️ LocationService: Attempt ${retryCount + 1} failed: $e');
          retryCount++;

          if (retryCount < maxRetries) {
            debugPrint('🔄 LocationService: Waiting before retry...');
            await Future.delayed(
                Duration(milliseconds: 1000 * retryCount)); // Progressive delay
          }
        }
      }

      if (position != null) {
        // Cache the location
        _lastKnownLocation = position;
        _lastLocationTime = DateTime.now();
        _isLocationInitialized = true;

        debugPrint(
            '✅ LocationService: First-time location successfully cached');
        return position;
      } else {
        debugPrint(
            '❌ LocationService: Failed to get location after $maxRetries attempts');
        return null;
      }
    } catch (e) {
      debugPrint('❌ LocationService: Error getting first-time location: $e');
      return null;
    }
  }

  /// Calculate distance between two coordinates in kilometers
  double calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
          point1.latitude,
          point1.longitude,
          point2.latitude,
          point2.longitude,
        ) /
        1000; // Convert meters to kilometers
  }

  /// Format distance for display
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      // If less than 1 km, show in meters
      return '${(distanceInKm * 1000).toStringAsFixed(0)} m';
    } else if (distanceInKm < 10) {
      // If less than 10 km, show with 1 decimal place
      return '${distanceInKm.toStringAsFixed(1)} km';
    } else {
      // Otherwise, show as integer
      return '${distanceInKm.toStringAsFixed(0)} km';
    }
  }
}
