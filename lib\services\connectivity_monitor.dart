import 'dart:async';
import 'package:flutter/material.dart';
import 'connectivity_service.dart';
import 'network_retry_service.dart';
import 'connectivity_error_service.dart';

/// Connectivity monitor with intelligent error handling
/// Replaces the global connectivity monitor with debounced, context-aware error handling
class ConnectivityMonitor {
  // Singleton pattern
  static final ConnectivityMonitor _instance = ConnectivityMonitor._internal();
  factory ConnectivityMonitor() => _instance;
  ConnectivityMonitor._internal();

  // Services
  final ConnectivityService _connectivityService = ConnectivityService();
  final NetworkRetryService _retryService = NetworkRetryService();

  // State management
  bool _isInitialized = false;
  bool _isErrorPageShowing = false;
  bool _isDisposed = false;

  // Subscriptions
  StreamSubscription<ConnectionStatus>? _statusSubscription;
  StreamSubscription<ConnectionQuality>? _qualitySubscription;

  // Context management
  BuildContext? _currentContext;
  GlobalKey<NavigatorState>? _navigatorKey;

  // Error handling state
  Timer? _errorDisplayTimer;
  ConnectionStatus? _lastReportedStatus;
  DateTime? _lastErrorTime;
  String? _routeBeforeError; // Track the route that was replaced by error page

  // Configuration
  static const Duration _errorDisplayDelay =
      Duration(seconds: 2); // Reduced delay for faster response
  static const Duration _errorPageMinDuration =
      Duration(seconds: 1); // Reduced minimum duration
  static const Duration _connectionRestoreDelay =
      Duration(milliseconds: 500); // Delay before hiding error page

  /// Initialize the connectivity monitor with navigation key
  static void initialize(GlobalKey<NavigatorState> navigatorKey) {
    final instance = ConnectivityMonitor();
    instance._navigatorKey = navigatorKey;
    instance._initializeMonitoring();
  }

  /// Set current context for error handling
  void setContext(BuildContext context) {
    _currentContext = context;
  }

  /// Initialize monitoring with enhanced logic
  void _initializeMonitoring() {
    if (_isInitialized || _isDisposed) return;

    debugPrint('🌐 CONNECTIVITY MONITOR: Initializing...');

    // Initialize the connectivity service
    _connectivityService.initialize();

    // Listen to connection status changes
    _statusSubscription = _connectivityService.connectionStatus.listen(
      _handleStatusChange,
      onError: (error) {
        debugPrint('🌐 CONNECTIVITY MONITOR: Status stream error: $error');
      },
    );

    // Listen to connection quality changes
    _qualitySubscription = _connectivityService.connectionQuality.listen(
      _handleQualityChange,
      onError: (error) {
        debugPrint('🌐 CONNECTIVITY MONITOR: Quality stream error: $error');
      },
    );

    _isInitialized = true;
    debugPrint('🌐 CONNECTIVITY MONITOR: Initialization complete');
  }

  /// Handle connection status changes with intelligent error display
  void _handleStatusChange(ConnectionStatus status) {
    debugPrint('🌐 CONNECTIVITY MONITOR: Status changed to: $status');
    debugPrint(
        '🌐 CONNECTIVITY MONITOR: Error page showing: $_isErrorPageShowing');
    debugPrint(
        '🌐 CONNECTIVITY MONITOR: Last reported status: $_lastReportedStatus');

    // Cancel any pending error display
    _errorDisplayTimer?.cancel();

    if (status == ConnectionStatus.disconnected) {
      // Connection lost - show error page after delay
      _errorDisplayTimer = Timer(_errorDisplayDelay, () {
        if (!_isDisposed && status == _connectivityService.currentStatus) {
          _showConnectivityErrorPage();
        }
      });
    } else if (status == ConnectionStatus.connected && _isErrorPageShowing) {
      // Connection restored - verify connection before hiding error page
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: Connection restored, verifying before hiding error page');
      _verifyConnectionAndHideErrorPage();
    }

    _lastReportedStatus = status;
  }

  /// Verify connection is stable before hiding error page
  void _verifyConnectionAndHideErrorPage() async {
    try {
      // Wait a brief moment to ensure connection is stable
      await Future.delayed(_connectionRestoreDelay);

      // Double-check connection status using comprehensive verification
      final hasConnection =
          await _connectivityService.verifyInternetConnectivity();

      if (hasConnection && _isErrorPageShowing) {
        debugPrint(
            '🌐 CONNECTIVITY MONITOR: Connection verified, hiding error page');
        _hideConnectivityErrorPage();
      } else if (!hasConnection) {
        debugPrint(
            '🌐 CONNECTIVITY MONITOR: Connection verification failed, keeping error page');
      }
    } catch (e) {
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: Error during connection verification: $e');
      // If verification fails, assume connection is still unstable
    }
  }

  /// Handle connection quality changes
  void _handleQualityChange(ConnectionQuality quality) {
    debugPrint('🌐 CONNECTIVITY MONITOR: Quality changed to: $quality');

    // Could implement quality-based UI adjustments here
    // For example, show warnings for poor quality connections
  }

  /// Show connectivity error page with debouncing
  void _showConnectivityErrorPage() {
    if (_isErrorPageShowing || _isDisposed) return;

    final context = _getNavigationContext();
    if (context == null) {
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: No context available for error page');
      return;
    }

    // Store the current route name before showing error page
    final currentRoute = ModalRoute.of(context)?.settings.name;
    _routeBeforeError = currentRoute;

    debugPrint('🌐 CONNECTIVITY MONITOR: Showing connectivity error page');
    debugPrint(
        '🌐 CONNECTIVITY MONITOR: Current route before error: $_routeBeforeError');
    _isErrorPageShowing = true;
    _lastErrorTime = DateTime.now();

    ConnectivityErrorService.showConnectivityError(
      context,
      customMessage:
          'Connection lost. The app will automatically reconnect when your internet is restored.',
      onRetry: () async {
        debugPrint('🌐 CONNECTIVITY MONITOR: Manual retry triggered');
        final hasConnection =
            await _connectivityService.verifyInternetConnectivity();
        if (hasConnection) {
          _hideConnectivityErrorPage();
        }
      },
      replaceCurrentRoute: true,
    );
  }

  /// Hide connectivity error page
  void _hideConnectivityErrorPage() {
    if (!_isErrorPageShowing) return;

    // Check minimum display duration
    if (_lastErrorTime != null) {
      final elapsed = DateTime.now().difference(_lastErrorTime!);
      if (elapsed < _errorPageMinDuration) {
        // Wait for minimum duration before hiding
        Timer(_errorPageMinDuration - elapsed, () {
          _performErrorPageHide();
        });
        return;
      }
    }

    _performErrorPageHide();
  }

  /// Actually perform the error page hide operation
  void _performErrorPageHide() {
    if (!_isErrorPageShowing) return;

    debugPrint('🌐 CONNECTIVITY MONITOR: Hiding connectivity error page');
    debugPrint(
        '🌐 CONNECTIVITY MONITOR: Route before error was: $_routeBeforeError');

    final context = _getNavigationContext();
    if (context == null) {
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: No context available for hiding error page');
      _isErrorPageShowing = false;
      return;
    }

    // Check if we're currently on the connectivity error page
    final currentRoute = ModalRoute.of(context)?.settings.name;
    debugPrint('🌐 CONNECTIVITY MONITOR: Current route: $currentRoute');

    _isErrorPageShowing = false;

    // Strategy 1: If we have a specific route to go back to, navigate there
    if (_routeBeforeError != null && _routeBeforeError!.isNotEmpty) {
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: Navigating back to: $_routeBeforeError');
      Navigator.of(context).pushReplacementNamed(_routeBeforeError!);
    }
    // Strategy 2: If we can pop, do so
    else if (Navigator.canPop(context)) {
      debugPrint('🌐 CONNECTIVITY MONITOR: Popping current route');
      Navigator.of(context).pop();
    }
    // Strategy 3: Navigate to home/dashboard as fallback
    else {
      debugPrint('🌐 CONNECTIVITY MONITOR: Fallback - navigating to dashboard');
      Navigator.of(context).pushReplacementNamed('/dashboard');
    }

    // Clear the stored route
    _routeBeforeError = null;
  }

  /// Get navigation context
  BuildContext? _getNavigationContext() {
    if (_navigatorKey?.currentContext != null) {
      return _navigatorKey!.currentContext!;
    }

    if (_currentContext != null && _currentContext!.mounted) {
      return _currentContext;
    }

    return null;
  }

  /// Execute API call with enhanced retry logic
  Future<T> executeApiCall<T>(
    Future<T> Function() apiCall, {
    BuildContext? context,
    String? errorMessage,
    bool showErrorOnFailure = true,
  }) async {
    try {
      return await _retryService.executeWithConnectivityRetry<T>(
        apiCall,
        onRetry: (attempt, error) {
          debugPrint(
              '🌐 CONNECTIVITY MONITOR: API retry attempt $attempt: $error');
        },
      );
    } catch (error) {
      debugPrint('🌐 CONNECTIVITY MONITOR: API call failed: $error');

      if (showErrorOnFailure &&
          context != null &&
          ConnectivityErrorService.isConnectivityError(error)) {
        final message = errorMessage ??
            'Connection problem. Please check your internet and try again.';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            action: SnackBarAction(
              label: 'RETRY',
              textColor: Colors.white,
              onPressed: () async {
                try {
                  await executeApiCall<T>(apiCall,
                      context: context, errorMessage: errorMessage);
                } catch (e) {
                  debugPrint('🌐 CONNECTIVITY MONITOR: Retry failed: $e');
                }
              },
            ),
          ),
        );
      }

      rethrow;
    }
  }

  /// Execute station pagination API call with enhanced connectivity and retry logic
  /// Supports dynamic URL construction with location and power output parameters
  Future<T> executeStationPaginationCall<T>(
    Future<T> Function() apiCall, {
    BuildContext? context,
    String? errorMessage,
    bool showErrorOnFailure = true,
  }) async {
    try {
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: Executing station pagination API call');

      return await _connectivityService.executeStationPaginationCall<T>(
        apiCall,
        errorMessage: errorMessage,
      );
    } catch (error) {
      debugPrint(
          '🌐 CONNECTIVITY MONITOR: Station pagination API call failed: $error');

      if (showErrorOnFailure &&
          context != null &&
          ConnectivityErrorService.isConnectivityError(error)) {
        final message = errorMessage ??
            'Unable to load station data. Please check your internet connection.';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            action: SnackBarAction(
              label: 'RETRY',
              textColor: Colors.white,
              onPressed: () async {
                try {
                  await executeStationPaginationCall<T>(
                    apiCall,
                    context: context,
                    errorMessage: errorMessage,
                    showErrorOnFailure: false, // Avoid infinite retry loops
                  );
                } catch (e) {
                  debugPrint(
                      '🌐 CONNECTIVITY MONITOR: Station pagination retry failed: $e');
                }
              },
            ),
          ),
        );
      }

      rethrow;
    }
  }

  /// Get current connectivity status
  ConnectionStatus get currentStatus => _connectivityService.currentStatus;

  /// Get current connectivity quality
  ConnectionQuality get currentQuality => _connectivityService.currentQuality;

  /// Check if we have a good connection
  bool get hasGoodConnection => _connectivityService.hasGoodConnection;

  /// Check if error page is showing
  bool get isErrorPageShowing => _isErrorPageShowing;

  /// Manually trigger connectivity check
  Future<bool> checkConnectivity() async {
    return await _connectivityService.checkConnectionManually();
  }

  /// Force hide the connectivity error page (useful for debugging)
  void forceHideErrorPage() {
    debugPrint('🌐 CONNECTIVITY MONITOR: Force hiding error page');
    if (_isErrorPageShowing) {
      _hideConnectivityErrorPage();
    }
  }

  /// Dispose of all resources
  void dispose() {
    if (_isDisposed) return;

    debugPrint('🌐 CONNECTIVITY MONITOR: Disposing...');

    _isDisposed = true;
    _errorDisplayTimer?.cancel();
    _statusSubscription?.cancel();
    _qualitySubscription?.cancel();
    _connectivityService.dispose();

    debugPrint('🌐 CONNECTIVITY MONITOR: Disposed');
  }
}
